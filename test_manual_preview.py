#!/usr/bin/env python3
"""
Quick test of manual preview mode
"""

from complete_shooting_analyzer import ShootingAnalyzer
import cv2
import numpy as np

def test_manual_preview():
    """Test manual preview with a simple image"""
    print("Testing manual preview mode...")
    
    # Create analyzer
    analyzer = ShootingAnalyzer()
    
    # Set manual close mode
    analyzer.set_preview_mode('final_only')  # Only show final results
    analyzer.set_auto_close_delay(0)  # Manual close
    
    # Create a simple test image
    test_image = np.zeros((300, 400, 3), dtype=np.uint8)
    test_image[100:200, 150:250] = [0, 255, 0]  # Green rectangle
    cv2.circle(test_image, (200, 150), 10, (255, 255, 255), -1)  # White circle (bullet hole)
    
    # Save test image
    cv2.imwrite("test_target.jpg", test_image)
    
    print("Running analysis with manual preview...")
    print("You should see preview windows that require pressing Enter to continue")
    
    # Run analysis
    success = analyzer.run_complete_analysis("test_target.jpg")
    
    if success:
        print("✅ Manual preview test completed successfully!")
    else:
        print("❌ Manual preview test failed!")
    
    # Clean up
    import os
    if os.path.exists("test_target.jpg"):
        os.remove("test_target.jpg")

if __name__ == "__main__":
    test_manual_preview()
