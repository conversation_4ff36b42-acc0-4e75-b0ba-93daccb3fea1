#!/usr/bin/env python3
"""
Test script to demonstrate Canny edge detection for bullet hole detection
"""

from complete_shooting_analyzer import ShootingAnalyzer
import cv2
import numpy as np
import os

def create_test_target():
    """Create a test target image with various bullet holes"""
    print("Creating test target with different types of bullet holes...")
    
    # Create a green target background
    target = np.zeros((600, 800, 3), dtype=np.uint8)
    target[:, :] = [0, 150, 0]  # Green background
    
    # Add some noise/texture
    noise = np.random.randint(-20, 20, target.shape, dtype=np.int16)
    target = np.clip(target.astype(np.int16) + noise, 0, 255).astype(np.uint8)
    
    # Create different types of bullet holes
    
    # 1. Clear white holes (easy to detect)
    cv2.circle(target, (200, 150), 8, (255, 255, 255), -1)  # Filled white circle
    cv2.circle(target, (350, 200), 6, (255, 255, 255), -1)
    cv2.circle(target, (180, 300), 7, (255, 255, 255), -1)
    
    # 2. Dark holes (black spots)
    cv2.circle(target, (450, 180), 9, (0, 0, 0), -1)  # Black hole
    cv2.circle(target, (520, 250), 5, (20, 20, 20), -1)  # Dark gray hole
    
    # 3. Edge-defined holes (rings - good for Canny detection)
    cv2.circle(target, (300, 350), 10, (0, 0, 0), 2)  # Black ring
    cv2.circle(target, (400, 400), 8, (255, 255, 255), 2)  # White ring
    cv2.circle(target, (150, 450), 6, (100, 100, 100), 2)  # Gray ring
    
    # 4. Irregular holes (torn edges - challenging)
    # Create irregular shape using random points
    pts = np.array([[600, 200], [615, 195], [625, 205], [620, 220], [605, 225], [590, 215]], np.int32)
    cv2.fillPoly(target, [pts], (0, 0, 0))
    
    # 5. Faint holes (low contrast - edge detection should help)
    cv2.circle(target, (250, 500), 7, (80, 120, 80), -1)  # Slightly darker green
    cv2.circle(target, (500, 450), 6, (120, 180, 120), -1)  # Slightly lighter green
    
    # Add target center
    cv2.circle(target, (400, 300), 3, (255, 0, 0), -1)  # Red center dot
    cv2.circle(target, (400, 300), 50, (255, 255, 255), 1)  # White center ring
    
    return target

def test_traditional_vs_canny():
    """Test traditional detection vs Canny edge detection"""
    print("Testing Traditional vs Canny Edge Detection")
    print("=" * 60)
    
    # Create test target
    test_image = create_test_target()
    cv2.imwrite("test_canny_target.jpg", test_image)
    print("✅ Created test target: test_canny_target.jpg")
    
    # Test 1: Traditional detection only
    print("\n🔍 Test 1: Traditional Detection Only")
    analyzer_traditional = ShootingAnalyzer()
    analyzer_traditional.config['bullet_detection']['use_canny_edge'] = False
    analyzer_traditional.set_preview_mode('final_only')
    analyzer_traditional.set_auto_close_delay(0)  # Manual close for inspection
    
    success1 = analyzer_traditional.run_complete_analysis("test_canny_target.jpg")
    
    if success1:
        traditional_bullets = len(analyzer_traditional.results.get('bullets', []))
        print(f"   Traditional detection found: {traditional_bullets} bullets")
    
    # Test 2: Canny edge detection enabled
    print("\n🎯 Test 2: Enhanced Detection (Traditional + Canny)")
    analyzer_canny = ShootingAnalyzer()
    analyzer_canny.config['bullet_detection']['use_canny_edge'] = True
    analyzer_canny.config['bullet_detection']['combine_methods'] = True
    analyzer_canny.set_preview_mode('final_only')
    analyzer_canny.set_auto_close_delay(0)  # Manual close for inspection
    
    success2 = analyzer_canny.run_complete_analysis("test_canny_target.jpg")
    
    if success2:
        canny_bullets = len(analyzer_canny.results.get('bullets', []))
        edge_bullets = len([b for b in analyzer_canny.results.get('bullets', []) if b.type == 'edge_hole'])
        print(f"   Enhanced detection found: {canny_bullets} bullets")
        print(f"   Edge-detected bullets: {edge_bullets}")
    
    # Test 3: Canny only (no traditional)
    print("\n⚡ Test 3: Canny Edge Detection Only")
    analyzer_edge_only = ShootingAnalyzer()
    analyzer_edge_only.config['bullet_detection']['use_canny_edge'] = True
    analyzer_edge_only.config['bullet_detection']['combine_methods'] = False
    analyzer_edge_only.set_preview_mode('none')  # No preview for this test
    
    # Manually run edge detection
    image = cv2.imread("test_canny_target.jpg")
    if image is not None:
        # Crop to simulate target area
        cropped = image[50:550, 100:700]  # Simulate cropped target
        doubled = cv2.resize(cropped, (1200, 1000))  # Simulate size doubling
        gray = cv2.cvtColor(doubled, cv2.COLOR_BGR2GRAY)
        
        edges = analyzer_edge_only._apply_canny_edge_detection(gray)
        edge_holes = analyzer_edge_only._detect_holes_from_edges(edges, 10, 2000)
        
        print(f"   Edge-only detection found: {len(edge_holes)} bullets")
        
        # Save edge detection result
        cv2.imwrite("test_canny_edges_only.jpg", edges)
        print("   Saved edge detection: test_canny_edges_only.jpg")
    
    # Summary
    print("\n" + "=" * 60)
    print("📊 DETECTION COMPARISON SUMMARY")
    print("=" * 60)
    
    if success1 and success2:
        print(f"Traditional only:     {traditional_bullets} bullets")
        print(f"Enhanced (Trad+Edge): {canny_bullets} bullets")
        improvement = canny_bullets - traditional_bullets
        if improvement > 0:
            print(f"✅ Improvement: +{improvement} bullets detected with Canny edge detection")
        elif improvement < 0:
            print(f"⚠️  Difference: {improvement} bullets (edge detection filtered some false positives)")
        else:
            print("➡️  Same number detected (edge detection confirmed traditional results)")
    
    # Cleanup
    cleanup_files = ["test_canny_target.jpg", "test_canny_edges_only.jpg"]
    for file in cleanup_files:
        if os.path.exists(file):
            os.remove(file)
    
    print(f"\n🧹 Cleaned up test files")
    print("\n💡 Check the analysis_output/ directory for detailed results!")

def test_canny_parameters():
    """Test different Canny edge detection parameters"""
    print("\n🔧 Testing Different Canny Parameters")
    print("=" * 60)
    
    # Create simple test image
    test_img = np.zeros((200, 300), dtype=np.uint8)
    test_img[:, :] = 128  # Gray background
    cv2.circle(test_img, (150, 100), 20, 255, 2)  # White ring
    cv2.circle(test_img, (100, 150), 15, 0, -1)   # Black filled circle
    
    analyzer = ShootingAnalyzer()
    
    # Test different parameter combinations
    test_params = [
        (30, 100, "Low sensitivity"),
        (50, 150, "Medium sensitivity (default)"),
        (70, 200, "High sensitivity"),
        (100, 250, "Very high sensitivity")
    ]
    
    for low, high, description in test_params:
        analyzer.config['bullet_detection']['canny_low_threshold'] = low
        analyzer.config['bullet_detection']['canny_high_threshold'] = high
        
        edges = analyzer._apply_canny_edge_detection(test_img)
        edge_pixels = np.sum(edges > 0)
        
        print(f"  {description:20} (L:{low:3}, H:{high:3}): {edge_pixels:5} edge pixels")
    
    print("\n💡 Lower thresholds = more edges detected")
    print("💡 Higher thresholds = only strong edges detected")

if __name__ == "__main__":
    print("🎯 Canny Edge Detection Test Suite")
    print("=" * 60)
    
    try:
        test_traditional_vs_canny()
        test_canny_parameters()
        
        print("\n✅ All tests completed!")
        print("\n📋 What to look for in the previews:")
        print("   🟢 Green boxes: Traditional white hole detection")
        print("   🔴 Red boxes: Traditional black hole detection") 
        print("   🟣 Magenta boxes: Canny edge detection")
        print("   📊 Check confidence scores and detection counts")
        
    except Exception as e:
        print(f"\n❌ Test failed with error: {str(e)}")
        import traceback
        traceback.print_exc()
