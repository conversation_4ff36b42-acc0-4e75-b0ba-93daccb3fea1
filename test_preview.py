#!/usr/bin/env python3
"""
Test script to verify preview functionality works correctly
"""

import cv2
import numpy as np
import matplotlib
import os
import sys

# Test matplotlib backend availability
def test_matplotlib_backends():
    """Test which matplotlib backends are available"""
    print("Testing matplotlib backends...")
    
    backends_to_test = ['TkAgg', 'Qt5Agg', 'Qt4Agg', 'GTKAgg', 'Agg']
    working_backends = []
    
    for backend in backends_to_test:
        try:
            matplotlib.use(backend, force=True)
            import matplotlib.pyplot as plt
            
            # Try to create a simple figure
            fig = plt.figure(figsize=(2, 2))
            plt.close(fig)
            
            working_backends.append(backend)
            print(f"✅ {backend} - Working")
            
        except Exception as e:
            print(f"❌ {backend} - Failed: {str(e)}")
    
    return working_backends

def test_simple_preview():
    """Test simple image preview functionality"""
    print("\nTesting simple image preview...")
    
    try:
        # Set a working backend
        working_backends = test_matplotlib_backends()
        if not working_backends:
            print("❌ No working matplotlib backends found!")
            return False
        
        # Use the first working backend
        matplotlib.use(working_backends[0], force=True)
        import matplotlib.pyplot as plt
        
        # Create a test image
        test_image = np.random.randint(0, 255, (200, 300, 3), dtype=np.uint8)
        
        # Try to display it
        plt.ion()
        fig, ax = plt.subplots(figsize=(6, 4))
        ax.imshow(test_image)
        ax.set_title("Test Image Preview")
        ax.axis('off')
        
        plt.tight_layout()
        plt.draw()
        plt.show(block=False)
        
        print("✅ Test image displayed successfully!")
        print("   If you can see a colorful noise image, previews should work.")
        
        # Auto-close after 3 seconds
        plt.pause(3)
        plt.close(fig)
        
        return True
        
    except Exception as e:
        print(f"❌ Simple preview test failed: {str(e)}")
        return False

def test_opencv_display():
    """Test OpenCV display functionality as alternative"""
    print("\nTesting OpenCV display...")
    
    try:
        # Create a test image
        test_image = np.random.randint(0, 255, (200, 300, 3), dtype=np.uint8)
        
        # Add text to the image
        cv2.putText(test_image, "OpenCV Test", (50, 100), 
                   cv2.FONT_HERSHEY_SIMPLEX, 1, (255, 255, 255), 2)
        
        # Try to display with OpenCV
        cv2.imshow("OpenCV Test Window", test_image)
        print("✅ OpenCV window created successfully!")
        print("   If you can see a window with 'OpenCV Test' text, OpenCV display works.")
        
        # Wait for 3 seconds or key press
        cv2.waitKey(3000)
        cv2.destroyAllWindows()
        
        return True
        
    except Exception as e:
        print(f"❌ OpenCV display test failed: {str(e)}")
        return False

def create_fallback_preview_function():
    """Create a fallback preview function using OpenCV"""
    print("\nCreating fallback preview function...")
    
    fallback_code = '''
def show_image_opencv(image, title="Image Preview", wait_time=3000):
    """Fallback preview function using OpenCV"""
    try:
        # Ensure image is in correct format
        if len(image.shape) == 3 and image.shape[2] == 3:
            # Already in BGR format for OpenCV
            display_image = image.copy()
        else:
            # Convert grayscale to BGR
            display_image = cv2.cvtColor(image, cv2.COLOR_GRAY2BGR)
        
        # Add title to image
        cv2.putText(display_image, title, (10, 30), 
                   cv2.FONT_HERSHEY_SIMPLEX, 0.7, (255, 255, 255), 2)
        
        # Show image
        cv2.imshow(title, display_image)
        cv2.waitKey(wait_time)
        cv2.destroyAllWindows()
        
        return True
    except Exception as e:
        print(f"OpenCV preview failed: {e}")
        return False
'''
    
    # Save fallback function to file
    with open("opencv_preview_fallback.py", "w") as f:
        f.write(fallback_code)
    
    print("✅ Fallback preview function saved to 'opencv_preview_fallback.py'")

def main():
    """Main test function"""
    print("🔍 Testing Preview Functionality")
    print("=" * 50)
    
    # Test matplotlib backends
    working_backends = test_matplotlib_backends()
    
    if working_backends:
        print(f"\n✅ Found {len(working_backends)} working matplotlib backend(s)")
        
        # Test simple preview
        if test_simple_preview():
            print("\n🎉 Matplotlib previews should work in the analyzer!")
        else:
            print("\n⚠️  Matplotlib preview test failed, trying OpenCV...")
            if test_opencv_display():
                print("\n💡 OpenCV display works - can use as fallback")
                create_fallback_preview_function()
            else:
                print("\n❌ Both matplotlib and OpenCV display failed")
    else:
        print("\n❌ No working matplotlib backends found")
        if test_opencv_display():
            print("\n💡 OpenCV display works - can use as fallback")
            create_fallback_preview_function()
        else:
            print("\n❌ No display options available")
    
    print("\n" + "=" * 50)
    print("Test completed!")
    
    # Provide recommendations
    print("\nRecommendations:")
    if working_backends:
        print("✅ Use the shooting analyzer with preview enabled")
        print("   python complete_shooting_analyzer.py your_image.jpg")
    else:
        print("⚠️  Install a GUI backend for matplotlib:")
        print("   pip install tkinter  # For TkAgg backend")
        print("   pip install PyQt5    # For Qt5Agg backend")
        print("   Or use --no-preview flag to disable previews")

if __name__ == "__main__":
    main()
