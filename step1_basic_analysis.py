import cv2
import numpy as np
import matplotlib.pyplot as plt

def analyze_image_basic(image_path):
    """Step 1: Basic image analysis"""
    print("="*60)
    print("STEP 1: BASIC IMAGE ANALYSIS")
    print("="*60)
    
    # Read the image
    image = cv2.imread(image_path)
    if image is None:
        print(f"Error: Could not read image {image_path}")
        return None
    
    # Get basic image information
    height, width, channels = image.shape
    print(f"Image size: {width} x {height} pixels")
    print(f"Channels: {channels} (BGR)")
    
    # Convert to different color spaces for analysis
    gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
    hsv = cv2.cvtColor(image, cv2.COLOR_BGR2HSV)
    
    # Analyze color distribution
    print(f"\nColor Analysis:")
    print(f"  Gray range: {gray.min()} to {gray.max()}")
    print(f"  HSV - H range: {hsv[:,:,0].min()} to {hsv[:,:,0].max()}")
    print(f"  HSV - S range: {hsv[:,:,1].min()} to {hsv[:,:,1].max()}")
    print(f"  HSV - V range: {hsv[:,:,2].min()} to {hsv[:,:,2].max()}")
    
    # Create basic visualizations
    fig, axes = plt.subplots(2, 3, figsize=(15, 10))
    
    # Original image
    axes[0,0].imshow(cv2.cvtColor(image, cv2.COLOR_BGR2RGB))
    axes[0,0].set_title('Original Image')
    axes[0,0].axis('off')
    
    # Grayscale
    axes[0,1].imshow(gray, cmap='gray')
    axes[0,1].set_title('Grayscale')
    axes[0,1].axis('off')
    
    # HSV channels
    axes[0,2].imshow(hsv[:,:,0], cmap='hsv')
    axes[0,2].set_title('HSV - Hue')
    axes[0,2].axis('off')
    
    axes[1,0].imshow(hsv[:,:,1], cmap='gray')
    axes[1,0].set_title('HSV - Saturation')
    axes[1,0].axis('off')
    
    axes[1,1].imshow(hsv[:,:,2], cmap='gray')
    axes[1,1].set_title('HSV - Value')
    axes[1,1].axis('off')
    
    # Color histogram
    axes[1,2].hist(gray.ravel(), bins=256, range=[0,256], alpha=0.7)
    axes[1,2].set_title('Grayscale Histogram')
    axes[1,2].set_xlabel('Pixel Value')
    axes[1,2].set_ylabel('Frequency')
    
    plt.tight_layout()
    plt.savefig('step1_basic_analysis.jpg', dpi=300, bbox_inches='tight')
    print(f"\nBasic analysis saved as: step1_basic_analysis.jpg")
    
    return {
        'image': image,
        'gray': gray,
        'hsv': hsv,
        'size': (width, height)
    }

def main():
    """Main function for Step 1"""
    image_path = "Shoot-image4.jpg"
    
    # Check if image exists
    import os
    if not os.path.exists(image_path):
        print(f"Error: {image_path} not found!")
        return
    
    # Run basic analysis
    results = analyze_image_basic(image_path)
    
    if results:
        print(f"\nStep 1 Complete!")
        print(f"Image analyzed: {image_path}")
        print(f"Size: {results['size'][0]} x {results['size'][1]} pixels")

if __name__ == "__main__":
    main() 