import cv2
import numpy as np
import json
import os

class CombinedShootingAnalyzer:
    def __init__(self):
        self.results = {}
        self.step_images = {}
    
    def step1_load_original_image(self, image_path):
        """Step 1: Load and display original image"""
        print("\n" + "="*60)
        print("STEP 1: LOAD ORIGINAL IMAGE")
        print("="*60)
        
        if not os.path.exists(image_path):
            print(f"Error: {image_path} not found!")
            return None
        
        image = cv2.imread(image_path)
        if image is None:
            print(f"Error: Could not load {image_path}")
            return None
        
        print(f"Image loaded: {image_path}")
        print(f"Image size: {image.shape[1]} x {image.shape[0]} pixels")
        
        # Save original image
        cv2.imwrite("step1_original.jpg", image)
        print("  Saved: step1_original.jpg")
        
        self.step_images['original'] = image
        return image
    
    def step2_detect_green_target(self, image):
        """Step 2: Detect green target areas"""
        print("\n" + "="*60)
        print("STEP 2: GREEN TARGET DETECTION")
        print("="*60)
        
        if image is None:
            print("No image available for green detection!")
            return None
        
        # Convert to HSV for better color detection
        hsv = cv2.cvtColor(image, cv2.COLOR_BGR2HSV)
        
        # Define green color range
        lower_green = np.array([35, 50, 50])
        upper_green = np.array([85, 255, 255])
        
        # Create mask for green areas
        green_mask = cv2.inRange(hsv, lower_green, upper_green)
        
        # Find contours
        contours, _ = cv2.findContours(green_mask, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
        
        if not contours:
            print("No green areas found!")
            return None
        
        # Find largest green area
        largest_contour = max(contours, key=cv2.contourArea)
        x, y, w, h = cv2.boundingRect(largest_contour)
        
        green_data = {
            'bbox': (x, y, w, h),
            'area': cv2.contourArea(largest_contour),
            'center': (x + w//2, y + h//2)
        }
        
        print(f"Green target detected:")
        print(f"  Position: ({x}, {y})")
        print(f"  Size: {w} x {h} pixels")
        print(f"  Area: {green_data['area']} pixels")
        
        # Create visualization
        vis_image = image.copy()
        cv2.rectangle(vis_image, (x, y), (x + w, y + h), (0, 255, 0), 3)
        cv2.putText(vis_image, f'Green Target: {w}x{h}', (x, y-10), 
                    cv2.FONT_HERSHEY_SIMPLEX, 1, (0, 255, 0), 2)
        
        cv2.imwrite("step2_green_detection.jpg", vis_image)
        print("  Saved: step2_green_detection.jpg")
        
        self.results['green_data'] = green_data
        return green_data
    
    def step3_crop_green_target(self, image, green_data):
        """Step 3: Crop the green target area"""
        print("\n" + "="*60)
        print("STEP 3: CROP GREEN TARGET")
        print("="*60)
        
        if image is None or green_data is None:
            print("No image or green data available!")
            return None
        
        x, y, w, h = green_data['bbox']
        
        # Crop the green target area
        cropped_image = image[y:y+h, x:x+w]
        
        print(f"Cropped green target area:")
        print(f"  Original position: ({x}, {y})")
        print(f"  Cropped size: {w} x {h} pixels")
        
        cv2.imwrite("step3_cropped_green.jpg", cropped_image)
        print("  Saved: step3_cropped_green.jpg")
        
        self.step_images['cropped'] = cropped_image
        return cropped_image
    
    def step4_double_image_size(self, cropped_image):
        """Step 4: Double the image size for better bullet detection"""
        print("\n" + "="*60)
        print("STEP 4: DOUBLE IMAGE SIZE")
        print("="*60)
        
        if cropped_image is None:
            print("No cropped image available!")
            return None
        
        h, w = cropped_image.shape[:2]
        new_h, new_w = h * 2, w * 2
        
        # Double the image size using cubic interpolation
        doubled_image = cv2.resize(cropped_image, (new_w, new_h), interpolation=cv2.INTER_CUBIC)
        
        print(f"Image size doubled:")
        print(f"  Original size: {w} x {h} pixels")
        print(f"  New size: {new_w} x {new_h} pixels")
        
        cv2.imwrite("step4_doubled_size.jpg", doubled_image)
        print("  Saved: step4_doubled_size.jpg")
        
        self.step_images['doubled'] = doubled_image
        return doubled_image
    
    def step5_convert_to_grayscale(self, doubled_image):
        """Step 5: Convert to grayscale for bullet detection"""
        print("\n" + "="*60)
        print("STEP 5: CONVERT TO GRAYSCALE")
        print("="*60)
        
        if doubled_image is None:
            print("No doubled image available!")
            return None
        
        # Convert to grayscale
        gray_image = cv2.cvtColor(doubled_image, cv2.COLOR_BGR2GRAY)
        
        print(f"Converted to grayscale:")
        print(f"  Size: {gray_image.shape[1]} x {gray_image.shape[0]} pixels")
        
        cv2.imwrite("step5_grayscale.jpg", gray_image)
        print("  Saved: step5_grayscale.jpg")
        
        self.step_images['grayscale'] = gray_image
        return gray_image
    
    def step6_fill_edges_black(self, gray_image, margin=100):
        """Step 6: Fill edges with black to isolate target area"""
        print("\n" + "="*60)
        print("STEP 6: FILL EDGES WITH BLACK")
        print("="*60)
        
        if gray_image is None:
            print("No grayscale image available!")
            return None
        
        h, w = gray_image.shape[:2]
        
        # Create a copy of the image
        result = gray_image.copy()
        
        # Fill top margin
        result[:margin, :] = 0
        
        # Fill bottom margin
        result[h-margin:, :] = 0
        
        # Fill left margin
        result[:, :margin] = 0
        
        # Fill right margin
        result[:, w-margin:] = 0
        
        print(f"Filled {margin} pixels from all sides with black")
        print(f"  Original size: {w} x {h} pixels")
        print(f"  Effective area: {w-2*margin} x {h-2*margin} pixels")
        
        cv2.imwrite("step6_edge_filled.jpg", result)
        print("  Saved: step6_edge_filled.jpg")
        
        self.step_images['edge_filled'] = result
        return result
    
    def step7_detect_all_bullets(self, edge_filled_image):
        """Step 7: Detect all bullets as both white and black holes"""
        print("\n" + "="*60)
        print("STEP 7: DETECT ALL BULLETS (WHITE & BLACK HOLES)")
        print("="*60)
        
        if edge_filled_image is None:
            print("No edge-filled image available!")
            return []
        
        bullets = []
        
        # 1. Detect WHITE HOLES (white spots on dark background)
        print("\n--- DETECTING WHITE HOLES ---")
        _, binary_white = cv2.threshold(edge_filled_image, 127, 255, cv2.THRESH_BINARY)
        
        # Find connected components (white spots)
        num_labels_white, labels_white, stats_white, centroids_white = cv2.connectedComponentsWithStats(binary_white, connectivity=8)
        
        print(f"Found {num_labels_white-1} white connected components")
        
        white_holes = []
        for i in range(1, num_labels_white):  # Skip background (label 0)
            area = stats_white[i, cv2.CC_STAT_AREA]
            x = stats_white[i, cv2.CC_STAT_LEFT]
            y = stats_white[i, cv2.CC_STAT_TOP]
            w = stats_white[i, cv2.CC_STAT_WIDTH]
            h = stats_white[i, cv2.CC_STAT_HEIGHT]
            
            print(f"  White Component {i}: Area = {area}, Position = ({x},{y}), Size = {w}x{h}")
            
            # Mark ALL white holes as bullets (no area filtering)
            white_hole = {
                'id': f"W{i}",
                'area': int(area),
                'position': (int(x), int(y)),
                'size': (int(w), int(h)),
                'center': (int(centroids_white[i][0]), int(centroids_white[i][1])),
                'type': 'white_hole'
            }
            white_holes.append(white_hole)
            bullets.append(white_hole)
        
        # 2. Detect BLACK HOLES (black spots on light background)
        print("\n--- DETECTING BLACK HOLES ---")
        _, binary_black = cv2.threshold(edge_filled_image, 127, 255, cv2.THRESH_BINARY_INV)
        
        # Find connected components (black spots)
        num_labels_black, labels_black, stats_black, centroids_black = cv2.connectedComponentsWithStats(binary_black, connectivity=8)
        
        print(f"Found {num_labels_black-1} black connected components")
        
        black_holes = []
        for i in range(1, num_labels_black):  # Skip background (label 0)
            area = stats_black[i, cv2.CC_STAT_AREA]
            x = stats_black[i, cv2.CC_STAT_LEFT]
            y = stats_black[i, cv2.CC_STAT_TOP]
            w = stats_black[i, cv2.CC_STAT_WIDTH]
            h = stats_black[i, cv2.CC_STAT_HEIGHT]
            
            print(f"  Black Component {i}: Area = {area}, Position = ({x},{y}), Size = {w}x{h}")
            
            # Mark ALL black holes as bullets (no area filtering)
            black_hole = {
                'id': f"B{i}",
                'area': int(area),
                'position': (int(x), int(y)),
                'size': (int(w), int(h)),
                'center': (int(centroids_black[i][0]), int(centroids_black[i][1])),
                'type': 'black_hole'
            }
            black_holes.append(black_hole)
            bullets.append(black_hole)
        
        # Create enhanced visualization
        vis_image = cv2.cvtColor(edge_filled_image, cv2.COLOR_GRAY2BGR)
        
        # Draw white holes in green with enhanced visibility
        for bullet in white_holes:
            x, y = bullet['position']
            w, h = bullet['size']
            # Draw filled rectangle with transparency
            overlay = vis_image.copy()
            cv2.rectangle(overlay, (x, y), (x + w, y + h), (0, 255, 0), -1)  # Filled
            cv2.addWeighted(overlay, 0.3, vis_image, 0.7, 0, vis_image)
            # Draw border
            cv2.rectangle(vis_image, (x, y), (x + w, y + h), (0, 255, 0), 3)
            # Add larger text with background
            text = f"{bullet['id']} ({w}x{h})"
            cv2.putText(vis_image, text, (x, y-10), cv2.FONT_HERSHEY_SIMPLEX, 0.8, (0, 255, 0), 2)
        
        # Draw black holes in red with enhanced visibility
        for bullet in black_holes:
            x, y = bullet['position']
            w, h = bullet['size']
            # Draw filled rectangle with transparency
            overlay = vis_image.copy()
            cv2.rectangle(overlay, (x, y), (x + w, y + h), (0, 0, 255), -1)  # Filled
            cv2.addWeighted(overlay, 0.3, vis_image, 0.7, 0, vis_image)
            # Draw border
            cv2.rectangle(vis_image, (x, y), (x + w, y + h), (0, 0, 255), 3)
            # Add larger text with background
            text = f"{bullet['id']} ({w}x{h})"
            cv2.putText(vis_image, text, (x, y-10), cv2.FONT_HERSHEY_SIMPLEX, 0.8, (0, 0, 255), 2)
        
        # Add summary text
        summary_text = f"White Holes: {len(white_holes)}, Black Holes: {len(black_holes)}, Total: {len(bullets)}"
        cv2.putText(vis_image, summary_text, (10, 30), cv2.FONT_HERSHEY_SIMPLEX, 1, (255, 255, 255), 2)
        
        cv2.imwrite("step7_bullet_detection.jpg", vis_image)
        
        # Also create a separate detailed visualization
        detailed_vis = cv2.cvtColor(edge_filled_image, cv2.COLOR_GRAY2BGR)
        
        # Draw detection areas with different colors and labels
        for i, bullet in enumerate(white_holes):
            x, y = bullet['position']
            w, h = bullet['size']
            cv2.rectangle(detailed_vis, (x, y), (x + w, y + h), (0, 255, 0), 4)
            cv2.putText(detailed_vis, f"WHITE {bullet['id']}", (x, y-15), 
                        cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 255, 0), 2)
            cv2.putText(detailed_vis, f"Area: {bullet['area']}px", (x, y+h+20), 
                        cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 255, 0), 1)
        
        for i, bullet in enumerate(black_holes):
            x, y = bullet['position']
            w, h = bullet['size']
            cv2.rectangle(detailed_vis, (x, y), (x + w, y + h), (0, 0, 255), 4)
            cv2.putText(detailed_vis, f"BLACK {bullet['id']}", (x, y-15), 
                        cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 0, 255), 2)
            cv2.putText(detailed_vis, f"Area: {bullet['area']}px", (x, y+h+20), 
                        cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 0, 255), 1)
        
        cv2.imwrite("step7_detailed_detection.jpg", detailed_vis)
        
        print(f"\nBullet detection results:")
        print(f"  White holes detected: {len(white_holes)} (ALL marked as bullets)")
        print(f"  Black holes detected: {len(black_holes)} (ALL marked as bullets)")
        print(f"  Total bullets: {len(bullets)}")
        print(f"  Note: All detected holes marked as bullets (no area filtering)")
        
        self.results['bullets'] = bullets
        self.results['white_holes'] = white_holes
        self.results['black_holes'] = black_holes
        return bullets
    
    def save_results(self):
        """Save all results to JSON file"""
        print("\n" + "="*60)
        print("SAVING RESULTS")
        print("="*60)
        
        # Convert numpy types to native Python types for JSON serialization
        results_for_json = {}
        
        if 'green_data' in self.results:
            green_data = self.results['green_data']
            results_for_json['green_data'] = {
                'bbox': tuple(int(x) for x in green_data['bbox']),
                'area': float(green_data['area']),
                'center': tuple(int(x) for x in green_data['center'])
            }
        
        if 'bullets' in self.results:
            bullets = []
            for bullet in self.results['bullets']:
                bullets.append({
                    'id': bullet['id'],  # Keep as string (W1, B1, etc.)
                    'center': tuple(int(x) for x in bullet['center']),
                    'position': tuple(int(x) for x in bullet['position']),
                    'size': tuple(int(x) for x in bullet['size']),
                    'area': float(bullet['area']),
                    'type': bullet['type']
                })
            results_for_json['bullets'] = bullets
        
        # Save to JSON
        with open("combined_analysis_results.json", "w") as f:
            json.dump(results_for_json, f, indent=2)
        
        print("  Saved: combined_analysis_results.json")
    
    def run_complete_analysis(self, image_path):
        """Run complete analysis from original image to bullet detection"""
        print("COMBINED SHOOTING ANALYSIS")
        print("="*60)
        print(f"Image: {image_path}")
        print("="*60)
        
        # Step 1: Load original image
        image = self.step1_load_original_image(image_path)
        if image is None:
            return
        
        # Step 2: Detect green target
        green_data = self.step2_detect_green_target(image)
        if green_data is None:
            return
        
        # Step 3: Crop green target
        cropped_image = self.step3_crop_green_target(image, green_data)
        if cropped_image is None:
            return
        
        # Step 4: Double image size
        doubled_image = self.step4_double_image_size(cropped_image)
        if doubled_image is None:
            return
        
        # Step 5: Convert to grayscale
        gray_image = self.step5_convert_to_grayscale(doubled_image)
        if gray_image is None:
            return
        
        # Step 6: Fill edges with black
        edge_filled_image = self.step6_fill_edges_black(gray_image, margin=100)
        if edge_filled_image is None:
            return
        
        # Step 7: Detect all bullets
        bullets = self.step7_detect_all_bullets(edge_filled_image)
        
        # Save results
        self.save_results()
        
        print("\n" + "="*60)
        print("ANALYSIS COMPLETE!")
        print("="*60)
        print(f"Bullets detected: {len(bullets)}")
        print("\nFiles created:")
        print("  - step1_original.jpg")
        print("  - step2_green_detection.jpg")
        print("  - step3_cropped_green.jpg")
        print("  - step4_doubled_size.jpg")
        print("  - step5_grayscale.jpg")
        print("  - step6_edge_filled.jpg")
        print("  - step7_bullet_detection.jpg")
        print("  - step7_detailed_detection.jpg")
        print("  - combined_analysis_results.json")

def main():
    """Main function"""
    analyzer = CombinedShootingAnalyzer()
    
    # Analyze Shoot-image4.jpg
    image_path = "Shoot-image4.jpg"
    
    if not os.path.exists(image_path):
        print(f"Error: {image_path} not found!")
        return
    
    analyzer.run_complete_analysis(image_path)

if __name__ == "__main__":
    main() 