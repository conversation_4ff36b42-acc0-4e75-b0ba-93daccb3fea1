import cv2
import numpy as np
import os

def preserve_center_hole():
    """Detect and preserve the black hole in the center of the white area"""
    print("="*60)
    print("PRESERVING CENTER BLACK HOLE")
    print("="*60)
    
    # Load the original edge-filled image
    image_path = "test_edge_filling_margin_100.jpg"
    if not os.path.exists(image_path):
        print(f"Error: {image_path} not found!")
        print("Please run test_edge_filling.py first.")
        return
    
    image = cv2.imread(image_path, cv2.IMREAD_GRAYSCALE)
    if image is None:
        print(f"Error: Could not load {image_path}")
        return
    
    print(f"Image loaded: {image_path}")
    print(f"Image size: {image.shape[1]} x {image.shape[0]} pixels")
    
    # Create binary image to find white areas
    _, binary = cv2.threshold(image, 140, 255, cv2.THRESH_BINARY)
    
    # Find the largest white area (target)
    contours, _ = cv2.findContours(binary, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
    
    if not contours:
        print("No white areas found!")
        return
    
    # Find largest contour
    largest_contour = max(contours, key=cv2.contourArea)
    x, y, w, h = cv2.boundingRect(largest_contour)
    
    print(f"Largest white area detected:")
    print(f"  Position: ({x}, {y})")
    print(f"  Size: {w} x {h} pixels")
    print(f"  Area: {w * h} pixels")
    
    # Extract the white area
    white_area = image[y:y+h, x:x+w]
    
    # Find black holes within the white area
    _, white_binary = cv2.threshold(white_area, 140, 255, cv2.THRESH_BINARY)
    
    # Invert to find black holes
    black_holes = cv2.bitwise_not(white_binary)
    
    # Find contours of black holes
    hole_contours, _ = cv2.findContours(black_holes, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
    
    print(f"Found {len(hole_contours)} black holes in the white area")
    
    # Find the largest black hole (center hole)
    if hole_contours:
        largest_hole = max(hole_contours, key=cv2.contourArea)
        hole_x, hole_y, hole_w, hole_h = cv2.boundingRect(largest_hole)
        
        print(f"Largest black hole:")
        print(f"  Position: ({hole_x}, {hole_y})")
        print(f"  Size: {hole_w} x {hole_h} pixels")
        print(f"  Area: {hole_w * hole_h} pixels")
        
        # Create a copy of the original image
        result_image = image.copy()
        
        # Expand the detected area by filling white around the edges
        expansion_margin = 20
        expanded_x = max(0, x - expansion_margin)
        expanded_y = max(0, y - expansion_margin)
        expanded_w = min(image.shape[1] - expanded_x, w + 2 * expansion_margin)
        expanded_h = min(image.shape[0] - expanded_y, h + 2 * expansion_margin)
        
        print(f"Expanded area:")
        print(f"  Position: ({expanded_x}, {expanded_y})")
        print(f"  Size: {expanded_w} x {expanded_h} pixels")
        
        # Fill the expanded area with white first
        result_image[expanded_y:expanded_y+expanded_h, expanded_x:expanded_x+expanded_w] = 255
        
        # Then invert the expanded area
        expanded_roi = result_image[expanded_y:expanded_y+expanded_h, expanded_x:expanded_x+expanded_w]
        expanded_roi = cv2.bitwise_not(expanded_roi)
        result_image[expanded_y:expanded_y+expanded_h, expanded_x:expanded_x+expanded_w] = expanded_roi
        
        # Now preserve the center black hole by making it white again
        # Calculate the position of the hole in the expanded area
        hole_in_expanded_x = hole_x + (x - expanded_x)
        hole_in_expanded_y = hole_y + (y - expanded_y)
        
        # Make the center hole white (preserve it)
        result_image[hole_in_expanded_y:hole_in_expanded_y+hole_h, hole_in_expanded_x:hole_in_expanded_x+hole_w] = 255
        
        # Save the result
        cv2.imwrite("preserved_center_hole.jpg", result_image)
        print("  Saved: preserved_center_hole.jpg")
        
        # Create visualization
        vis_image = cv2.cvtColor(image, cv2.COLOR_GRAY2BGR)
        
        # Draw original white area in green
        cv2.rectangle(vis_image, (x, y), (x + w, y + h), (0, 255, 0), 3)
        cv2.putText(vis_image, f'Original: {w}x{h}', (x, y-10), 
                    cv2.FONT_HERSHEY_SIMPLEX, 0.8, (0, 255, 0), 2)
        
        # Draw expanded area in red
        cv2.rectangle(vis_image, (expanded_x, expanded_y), (expanded_x + expanded_w, expanded_y + expanded_h), (0, 0, 255), 3)
        cv2.putText(vis_image, f'Expanded: {expanded_w}x{expanded_h}', (expanded_x, expanded_y-10), 
                    cv2.FONT_HERSHEY_SIMPLEX, 0.8, (0, 0, 255), 2)
        
        # Draw center hole in blue
        cv2.rectangle(vis_image, (x + hole_x, y + hole_y), (x + hole_x + hole_w, y + hole_y + hole_h), (255, 0, 0), 3)
        cv2.putText(vis_image, f'Center Hole: {hole_w}x{hole_h}', (x + hole_x, y + hole_y-10), 
                    cv2.FONT_HERSHEY_SIMPLEX, 0.8, (255, 0, 0), 2)
        
        cv2.imwrite("preserved_center_hole_vis.jpg", vis_image)
        print("  Saved: preserved_center_hole_vis.jpg")
        
        print("\n" + "="*60)
        print("CENTER HOLE PRESERVATION COMPLETE!")
        print("="*60)
        print("Files created:")
        print("  - preserved_center_hole.jpg (with preserved center hole)")
        print("  - preserved_center_hole_vis.jpg (visualization)")
        
    else:
        print("No black holes found in the white area!")

def main():
    """Main function"""
    preserve_center_hole()

if __name__ == "__main__":
    main() 