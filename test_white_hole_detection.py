#!/usr/bin/env python3
"""
Test script specifically for white hole detection using enhanced Canny edge detection
"""

from complete_shooting_analyzer import ShootingAnalyzer
import cv2
import numpy as np
import os

def create_white_hole_test_target():
    """Create a test target with various types of white holes"""
    print("Creating test target with white holes...")
    
    # Create a dark green target background
    target = np.zeros((600, 800, 3), dtype=np.uint8)
    target[:, :] = [0, 100, 0]  # Dark green background
    
    # Add some texture/noise to make it more realistic
    noise = np.random.randint(-15, 15, target.shape, dtype=np.int16)
    target = np.clip(target.astype(np.int16) + noise, 0, 255).astype(np.uint8)
    
    # Create different types of white holes
    
    # 1. Solid white holes (easy to detect)
    cv2.circle(target, (150, 150), 12, (255, 255, 255), -1)  # Large white hole
    cv2.circle(target, (300, 200), 8, (255, 255, 255), -1)   # Medium white hole
    cv2.circle(target, (450, 180), 6, (255, 255, 255), -1)   # Small white hole
    
    # 2. White holes with dark edges (ring-like)
    cv2.circle(target, (200, 350), 15, (255, 255, 255), -1)  # White center
    cv2.circle(target, (200, 350), 15, (50, 50, 50), 2)      # Dark edge
    
    cv2.circle(target, (400, 400), 10, (255, 255, 255), -1)  # White center
    cv2.circle(target, (400, 400), 10, (30, 30, 30), 2)      # Dark edge
    
    # 3. Faint white holes (low contrast)
    cv2.circle(target, (600, 250), 9, (180, 180, 180), -1)   # Light gray hole
    cv2.circle(target, (550, 350), 7, (200, 200, 200), -1)   # Lighter gray hole
    
    # 4. White holes with irregular shapes
    # Create irregular white patches
    pts1 = np.array([[100, 450], [120, 440], [130, 460], [115, 470], [95, 465]], np.int32)
    cv2.fillPoly(target, [pts1], (255, 255, 255))
    
    pts2 = np.array([[350, 500], [370, 495], [375, 515], [360, 520], [345, 510]], np.int32)
    cv2.fillPoly(target, [pts2], (240, 240, 240))
    
    # 5. White holes partially obscured
    cv2.circle(target, (500, 500), 10, (255, 255, 255), -1)  # White hole
    cv2.rectangle(target, (505, 495), (515, 505), (0, 80, 0), -1)  # Partial obstruction
    
    # Add target center for reference
    cv2.circle(target, (400, 300), 3, (255, 0, 0), -1)  # Red center dot
    cv2.circle(target, (400, 300), 50, (255, 255, 255), 1)  # White center ring
    
    return target

def test_white_hole_configurations():
    """Test different configurations for white hole detection"""
    print("Testing White Hole Detection Configurations")
    print("=" * 60)
    
    # Create test target
    test_image = create_white_hole_test_target()
    cv2.imwrite("test_white_holes.jpg", test_image)
    print("✅ Created test target: test_white_holes.jpg")
    
    # Configuration 1: Standard Canny (baseline)
    print("\n🔍 Test 1: Standard Canny Detection")
    analyzer1 = ShootingAnalyzer()
    analyzer1.config['bullet_detection']['use_canny_edge'] = True
    analyzer1.config['bullet_detection']['white_hole_focus'] = False
    analyzer1.config['bullet_detection']['canny_low_threshold'] = 50
    analyzer1.config['bullet_detection']['canny_high_threshold'] = 150
    analyzer1.set_preview_mode('none')
    
    success1 = analyzer1.run_complete_analysis("test_white_holes.jpg")
    standard_bullets = len(analyzer1.results.get('bullets', [])) if success1 else 0
    print(f"   Standard Canny detected: {standard_bullets} bullets")
    
    # Configuration 2: White hole focused detection
    print("\n🎯 Test 2: White Hole Focused Detection")
    analyzer2 = ShootingAnalyzer()
    analyzer2.config['bullet_detection']['use_canny_edge'] = True
    analyzer2.config['bullet_detection']['white_hole_focus'] = True
    analyzer2.config['bullet_detection']['invert_for_white_holes'] = True
    analyzer2.config['bullet_detection']['canny_low_threshold'] = 30
    analyzer2.config['bullet_detection']['canny_high_threshold'] = 100
    analyzer2.config['bullet_detection']['gaussian_sigma'] = 1.0
    analyzer2.set_preview_mode('none')
    
    success2 = analyzer2.run_complete_analysis("test_white_holes.jpg")
    white_focused_bullets = len(analyzer2.results.get('bullets', [])) if success2 else 0
    white_edge_bullets = len([b for b in analyzer2.results.get('bullets', []) if b.type == 'edge_hole']) if success2 else 0
    print(f"   White hole focused detected: {white_focused_bullets} bullets")
    print(f"   Edge-detected bullets: {white_edge_bullets}")
    
    # Configuration 3: High sensitivity for faint holes
    print("\n⚡ Test 3: High Sensitivity Detection")
    analyzer3 = ShootingAnalyzer()
    analyzer3.config['bullet_detection']['use_canny_edge'] = True
    analyzer3.config['bullet_detection']['white_hole_focus'] = True
    analyzer3.config['bullet_detection']['invert_for_white_holes'] = True
    analyzer3.config['bullet_detection']['canny_low_threshold'] = 20  # Very sensitive
    analyzer3.config['bullet_detection']['canny_high_threshold'] = 80
    analyzer3.config['bullet_detection']['confidence_threshold'] = 0.3  # Lower threshold
    analyzer3.config['bullet_detection']['gaussian_sigma'] = 0.8
    analyzer3.set_preview_mode('none')
    
    success3 = analyzer3.run_complete_analysis("test_white_holes.jpg")
    high_sens_bullets = len(analyzer3.results.get('bullets', [])) if success3 else 0
    high_sens_edge_bullets = len([b for b in analyzer3.results.get('bullets', []) if b.type == 'edge_hole']) if success3 else 0
    print(f"   High sensitivity detected: {high_sens_bullets} bullets")
    print(f"   Edge-detected bullets: {high_sens_edge_bullets}")
    
    # Summary
    print("\n" + "=" * 60)
    print("📊 WHITE HOLE DETECTION COMPARISON")
    print("=" * 60)
    print(f"Standard Canny:        {standard_bullets} bullets")
    print(f"White Hole Focused:    {white_focused_bullets} bullets (+{white_focused_bullets - standard_bullets})")
    print(f"High Sensitivity:      {high_sens_bullets} bullets (+{high_sens_bullets - standard_bullets})")
    
    # Determine best configuration
    if white_focused_bullets >= standard_bullets and white_edge_bullets > 0:
        print("\n✅ RECOMMENDATION: Use White Hole Focused Detection")
        print("   - Better detection of white holes")
        print("   - Enhanced edge detection for ring-shaped holes")
    elif high_sens_bullets > white_focused_bullets:
        print("\n✅ RECOMMENDATION: Use High Sensitivity Detection")
        print("   - Best for faint or low-contrast white holes")
        print("   - May have more false positives")
    else:
        print("\n➡️  RECOMMENDATION: Standard detection is sufficient")
        print("   - White holes are already well-detected")
    
    # Cleanup
    if os.path.exists("test_white_holes.jpg"):
        os.remove("test_white_holes.jpg")

def test_real_image_white_holes():
    """Test white hole detection on the real shooting image"""
    print("\n🎯 Testing White Hole Detection on Real Image")
    print("=" * 60)
    
    image_path = "Shoot-image4.jpg"
    if not os.path.exists(image_path):
        print(f"❌ Real image not found: {image_path}")
        return
    
    # Test 1: Traditional detection
    print("\n📊 Traditional Detection:")
    analyzer_trad = ShootingAnalyzer()
    analyzer_trad.config['bullet_detection']['use_canny_edge'] = False
    analyzer_trad.set_preview_mode('none')
    
    success_trad = analyzer_trad.run_complete_analysis(image_path)
    if success_trad:
        trad_bullets = analyzer_trad.results.get('bullets', [])
        trad_white = len([b for b in trad_bullets if b.type == 'white_hole'])
        print(f"   Total bullets: {len(trad_bullets)}")
        print(f"   White holes: {trad_white}")
        print(f"   Average confidence: {np.mean([b.confidence for b in trad_bullets]):.3f}")
    
    # Test 2: Enhanced white hole detection
    print("\n🎯 Enhanced White Hole Detection:")
    analyzer_enhanced = ShootingAnalyzer()
    analyzer_enhanced.config['bullet_detection']['use_canny_edge'] = True
    analyzer_enhanced.config['bullet_detection']['white_hole_focus'] = True
    analyzer_enhanced.config['bullet_detection']['invert_for_white_holes'] = True
    analyzer_enhanced.config['bullet_detection']['canny_low_threshold'] = 25
    analyzer_enhanced.config['bullet_detection']['canny_high_threshold'] = 90
    analyzer_enhanced.set_preview_mode('final_only')
    analyzer_enhanced.set_auto_close_delay(3)
    
    success_enhanced = analyzer_enhanced.run_complete_analysis(image_path)
    if success_enhanced:
        enhanced_bullets = analyzer_enhanced.results.get('bullets', [])
        enhanced_white = len([b for b in enhanced_bullets if b.type == 'white_hole'])
        enhanced_edge = len([b for b in enhanced_bullets if b.type == 'edge_hole'])
        print(f"   Total bullets: {len(enhanced_bullets)}")
        print(f"   White holes (traditional): {enhanced_white}")
        print(f"   White holes (edge-detected): {enhanced_edge}")
        print(f"   Average confidence: {np.mean([b.confidence for b in enhanced_bullets]):.3f}")
        
        # Check for new detections
        improvement = len(enhanced_bullets) - len(trad_bullets) if success_trad else 0
        if improvement > 0:
            print(f"   ✅ Found {improvement} additional bullets!")
        elif improvement < 0:
            print(f"   ⚠️  Filtered out {abs(improvement)} potential false positives")
        else:
            print(f"   ➡️  Same number detected (validation)")

def main():
    """Main test function"""
    print("🎯 White Hole Detection Test Suite")
    print("=" * 60)
    
    try:
        # Test with synthetic white holes
        test_white_hole_configurations()
        
        # Test with real image
        test_real_image_white_holes()
        
        print("\n✅ All white hole detection tests completed!")
        print("\n💡 Key Features Tested:")
        print("   🔍 Image inversion for white hole detection")
        print("   📊 Histogram equalization for contrast enhancement")
        print("   🎯 Intensity-based confidence scoring")
        print("   ⚙️  Optimized Canny parameters for white holes")
        print("   🔧 Morphological operations for edge enhancement")
        
    except Exception as e:
        print(f"\n❌ Test failed with error: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
