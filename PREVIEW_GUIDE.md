# 🖼️ Shooting Analyzer Preview Guide

## Overview

The Enhanced Shooting Analyzer now includes comprehensive live preview functionality that displays images at each processing step. This guide explains how to use and configure the preview system.

## ✅ Preview System Status

**WORKING**: The preview system is fully functional and tested!

- ✅ TkAgg matplotlib backend available
- ✅ OpenCV fallback working
- ✅ Auto-close and manual modes working
- ✅ All preview steps implemented

## 🎮 Quick Start

### Basic Usage with Previews

```bash
# Run with default previews (all steps, manual close - press Enter to continue)
python complete_shooting_analyzer.py your_image.jpg

# Key steps only with 2-second auto-close
python complete_shooting_analyzer.py your_image.jpg --preview-key --auto-close 2

# Auto-close all previews after 3 seconds
python complete_shooting_analyzer.py your_image.jpg --auto-close 3

# No previews for fast processing
python complete_shooting_analyzer.py your_image.jpg --no-preview
```

## 🎛️ Preview Modes

### 1. All Steps (`--preview-all`)

Shows previews for every processing step:

- Step 1: Original image loading
- Step 2: Green target detection (before/after comparison)
- Step 3: Target cropping (original with crop area + cropped result)
- Step 4: Image enhancement (size comparison)
- Step 5: Binary conversion (color → grayscale → binary)
- Step 6: Detection preparation
- Step 7: Bullet detection (comprehensive 3-panel view)

### 2. Key Steps (`--preview-key`)

Shows only the most important steps:

- Step 1: Original image
- Step 2: Target detection
- Step 5: Binary conversion
- Step 7: Final results

### 3. Final Only (`--preview-final`)

Shows only the final bullet detection results

### 4. No Previews (`--no-preview`)

Disables all previews for batch processing

## ⏱️ Timing Controls

### Manual Close Mode (Default)

```bash
# Wait for Enter key at each step (default behavior)
python complete_shooting_analyzer.py image.jpg

# Explicitly specify manual close
python complete_shooting_analyzer.py image.jpg --manual-close
```

### Auto-Close Mode

```bash
# Auto-close after 3 seconds
python complete_shooting_analyzer.py image.jpg --auto-close 3

# Auto-close after 5 seconds
python complete_shooting_analyzer.py image.jpg --auto-close 5
```

## 🔧 Configuration

### Create Custom Configuration

```bash
python complete_shooting_analyzer.py --create-config
```

This creates `analyzer_config.json` where you can customize:

```json
{
  "preview": {
    "enable_previews": true,
    "step1_original": true,
    "step2_green_detection": true,
    "step3_cropped": false,
    "step4_doubled": false,
    "step5_binary": true,
    "step7_bullets": true,
    "preview_size": [12, 9],
    "auto_close_delay": 0.0,
    "save_preview_images": true
  }
}
```

### Programmatic Control

```python
from complete_shooting_analyzer import ShootingAnalyzer

# Create analyzer
analyzer = ShootingAnalyzer()

# Configure previews
analyzer.set_preview_mode('key_steps')
analyzer.set_auto_close_delay(5.0)

# Run analysis
analyzer.run_complete_analysis("image.jpg")
```

## 🎨 Preview Features

### Visual Enhancements

- **Confidence-based styling**: Detection confidence affects color intensity and border thickness
- **Before/after comparisons**: Side-by-side views showing processing effects
- **Information overlays**: Image dimensions, processing parameters, detection statistics
- **Interactive windows**: Zoom, pan, and inspect details

### Fallback Systems

1. **Primary**: Matplotlib with TkAgg backend
2. **Fallback**: OpenCV display windows
3. **Last resort**: Save images to disk only

## 📊 What You'll See

### Step 1: Original Image

- Loaded image with file information
- Image dimensions and file size

### Step 2: Target Detection

- Original image vs detection result
- Confidence scores and detection metrics
- Multiple contour candidates highlighted

### Step 3: Target Cropping

- Original with highlighted crop area
- Cropped result showing target area

### Step 4: Image Enhancement

- Size comparison (original vs enhanced)
- Resolution improvement visualization

### Step 5: Binary Conversion

- Three-panel progression: Color → Grayscale → Binary
- Threshold values and pixel statistics

### Step 7: Bullet Detection

- Processed image
- Detection results with confidence-based styling
- Detailed analysis with individual bullet metrics

## 🚨 Troubleshooting

### Preview Not Showing?

1. **Check display**: Run `python test_preview.py` to test your system
2. **Install GUI backend**: `pip install tkinter` (usually included with Python)
3. **Use OpenCV fallback**: The system automatically tries OpenCV if matplotlib fails
4. **Disable previews**: Use `--no-preview` flag for headless operation

### Common Issues

- **"Display not available"**: Running on headless server → Use `--no-preview`
- **Windows close immediately**: Increase auto-close delay → `--auto-close 10`
- **Too many windows**: Use `--preview-key` or `--preview-final` for fewer previews

## 💡 Tips

### For Interactive Analysis

```bash
# Best for detailed inspection
python complete_shooting_analyzer.py image.jpg --preview-all --manual-close
```

### For Quick Review

```bash
# Fast overview of key steps
python complete_shooting_analyzer.py image.jpg --preview-key --auto-close 2
```

### For Batch Processing

```bash
# No interruptions
python complete_shooting_analyzer.py image.jpg --no-preview
```

### For Debugging

```bash
# See everything with debug info
python complete_shooting_analyzer.py image.jpg --preview-all --debug --manual-close
```

## 📁 Output Files

When `save_preview_images` is enabled, preview images are saved to `analysis_output/`:

- `preview_step1_*.png`
- `preview_step2_*.png`
- `preview_comparison_*.png`

## 🎯 Success Indicators

✅ **Working correctly if you see**:

- Preview windows opening automatically
- Images displaying clearly
- Auto-close working as expected
- Log messages: "Showing [Step Name] (auto-close in Xs)"

❌ **Issues if you see**:

- "Display not available" warnings
- "Preview failed" error messages
- No preview windows opening

## 📞 Support

If previews aren't working:

1. Run the test script: `python test_preview.py`
2. Check the troubleshooting section above
3. Use `--no-preview` as a workaround
4. The analysis will still work perfectly without previews!
