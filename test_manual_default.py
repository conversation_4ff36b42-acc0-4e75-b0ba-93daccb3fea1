#!/usr/bin/env python3
"""
Test that manual close is now the default behavior
"""

from complete_shooting_analyzer import <PERSON>Anal<PERSON><PERSON>

def test_default_behavior():
    """Test that the default configuration uses manual close"""
    print("Testing default preview behavior...")
    
    # Create analyzer with default config
    analyzer = ShootingAnalyzer()
    
    # Check the default auto_close_delay
    auto_close_delay = analyzer.config['preview']['auto_close_delay']
    
    print(f"Default auto_close_delay: {auto_close_delay}")
    
    if auto_close_delay == 0.0:
        print("✅ CORRECT: Default is manual close (auto_close_delay = 0.0)")
        print("   Previews will wait for Enter key by default")
    else:
        print(f"❌ INCORRECT: Default is auto-close ({auto_close_delay}s)")
        print("   Expected manual close (0.0)")
    
    # Test preview mode settings
    preview_config = analyzer.config['preview']
    enabled_steps = [key for key, value in preview_config.items() 
                    if key.startswith('step') and value]
    
    print(f"\nDefault enabled preview steps: {enabled_steps}")
    print(f"Preview master switch: {preview_config['enable_previews']}")
    
    return auto_close_delay == 0.0

def test_command_line_override():
    """Test that command line can still override to auto-close"""
    print("\nTesting command line override...")
    
    analyzer = ShootingAnalyzer()
    
    # Test setting auto-close programmatically
    analyzer.set_auto_close_delay(5.0)
    
    new_delay = analyzer.config['preview']['auto_close_delay']
    print(f"After setting auto-close to 5.0: {new_delay}")
    
    if new_delay == 5.0:
        print("✅ CORRECT: Can override to auto-close")
    else:
        print("❌ INCORRECT: Override failed")
    
    # Test setting back to manual
    analyzer.set_auto_close_delay(0.0)
    
    manual_delay = analyzer.config['preview']['auto_close_delay']
    print(f"After setting back to manual: {manual_delay}")
    
    if manual_delay == 0.0:
        print("✅ CORRECT: Can set back to manual")
    else:
        print("❌ INCORRECT: Manual setting failed")
    
    return new_delay == 5.0 and manual_delay == 0.0

if __name__ == "__main__":
    print("🔍 Testing Manual Close Default Behavior")
    print("=" * 50)
    
    test1_passed = test_default_behavior()
    test2_passed = test_command_line_override()
    
    print("\n" + "=" * 50)
    if test1_passed and test2_passed:
        print("✅ ALL TESTS PASSED!")
        print("   Default behavior is now manual close")
        print("   Command line overrides work correctly")
    else:
        print("❌ SOME TESTS FAILED!")
    
    print("\nUsage examples:")
    print("  python complete_shooting_analyzer.py image.jpg                    # Manual close (default)")
    print("  python complete_shooting_analyzer.py image.jpg --auto-close 3     # Auto-close after 3s")
    print("  python complete_shooting_analyzer.py image.jpg --manual-close     # Explicit manual close")
