#!/usr/bin/env python3
"""
Comprehensive comparison between Traditional and Canny Edge Detection methods
"""

from complete_shooting_analyzer import ShootingAnalyzer
import json
import os
from typing import Dict, List, Any

def run_traditional_detection(image_path: str) -> Dict[str, Any]:
    """Run analysis with traditional detection only"""
    print("🔍 Running Traditional Detection Analysis...")
    
    analyzer = ShootingAnalyzer()
    # Disable Canny edge detection
    analyzer.config['bullet_detection']['use_canny_edge'] = False
    analyzer.set_preview_mode('none')  # No previews for comparison
    
    success = analyzer.run_complete_analysis(image_path)
    
    if success:
        results = {
            'method': 'Traditional Only',
            'success': True,
            'bullets': analyzer.results.get('bullets', []),
            'total_bullets': len(analyzer.results.get('bullets', [])),
            'white_holes': len([b for b in analyzer.results.get('bullets', []) if b.type == 'white_hole']),
            'black_holes': len([b for b in analyzer.results.get('bullets', []) if b.type == 'black_hole']),
            'edge_holes': 0,
            'avg_confidence': sum(b.confidence for b in analyzer.results.get('bullets', [])) / len(analyzer.results.get('bullets', [])) if analyzer.results.get('bullets') else 0,
            'detection_stats': analyzer.results.get('detection_stats', {}),
            'target_confidence': analyzer.results.get('green_data').confidence if analyzer.results.get('green_data') else 0
        }
        
        # Save traditional results
        with open('traditional_results.json', 'w') as f:
            json.dump({
                'bullets': [
                    {
                        'id': b.id,
                        'type': b.type,
                        'center': b.center,
                        'area': b.area,
                        'confidence': b.confidence
                    } for b in analyzer.results.get('bullets', [])
                ],
                'stats': results['detection_stats']
            }, f, indent=2)
        
        return results
    else:
        return {'method': 'Traditional Only', 'success': False}

def run_enhanced_detection(image_path: str) -> Dict[str, Any]:
    """Run analysis with enhanced detection (Traditional + Canny)"""
    print("🎯 Running Enhanced Detection Analysis (Traditional + Canny)...")
    
    analyzer = ShootingAnalyzer()
    # Enable Canny edge detection
    analyzer.config['bullet_detection']['use_canny_edge'] = True
    analyzer.config['bullet_detection']['combine_methods'] = True
    analyzer.set_preview_mode('none')  # No previews for comparison
    
    success = analyzer.run_complete_analysis(image_path)
    
    if success:
        bullets = analyzer.results.get('bullets', [])
        results = {
            'method': 'Enhanced (Traditional + Canny)',
            'success': True,
            'bullets': bullets,
            'total_bullets': len(bullets),
            'white_holes': len([b for b in bullets if b.type == 'white_hole']),
            'black_holes': len([b for b in bullets if b.type == 'black_hole']),
            'edge_holes': len([b for b in bullets if b.type == 'edge_hole']),
            'avg_confidence': sum(b.confidence for b in bullets) / len(bullets) if bullets else 0,
            'detection_stats': analyzer.results.get('detection_stats', {}),
            'target_confidence': analyzer.results.get('green_data').confidence if analyzer.results.get('green_data') else 0
        }
        
        # Save enhanced results
        with open('enhanced_results.json', 'w') as f:
            json.dump({
                'bullets': [
                    {
                        'id': b.id,
                        'type': b.type,
                        'center': b.center,
                        'area': b.area,
                        'confidence': b.confidence
                    } for b in bullets
                ],
                'stats': results['detection_stats']
            }, f, indent=2)
        
        return results
    else:
        return {'method': 'Enhanced (Traditional + Canny)', 'success': False}

def run_canny_only_detection(image_path: str) -> Dict[str, Any]:
    """Run analysis with Canny edge detection only"""
    print("⚡ Running Canny Edge Detection Only...")
    
    analyzer = ShootingAnalyzer()
    # Enable Canny but disable combining (to see pure edge detection)
    analyzer.config['bullet_detection']['use_canny_edge'] = True
    analyzer.config['bullet_detection']['combine_methods'] = False
    analyzer.set_preview_mode('none')
    
    # Manually run the analysis steps to get edge-only results
    image = analyzer.step1_original_image(image_path)
    if image is None:
        return {'method': 'Canny Only', 'success': False}
    
    green_data = analyzer.step2_green_detection(image)
    if green_data is None:
        return {'method': 'Canny Only', 'success': False}
    
    cropped = analyzer.step3_crop_green_target(image, green_data)
    doubled = analyzer.step4_double_size(cropped)
    binary, gray = analyzer.step5_binary_conversion(doubled)
    processed_gray = analyzer.step6_skip_inversion(gray)
    
    # Apply edge detection
    edges = analyzer._apply_canny_edge_detection(processed_gray)
    edge_holes = analyzer._detect_holes_from_edges(edges, 10, 2000)
    
    results = {
        'method': 'Canny Edge Only',
        'success': True,
        'bullets': edge_holes,
        'total_bullets': len(edge_holes),
        'white_holes': 0,
        'black_holes': 0,
        'edge_holes': len(edge_holes),
        'avg_confidence': sum(b.confidence for b in edge_holes) / len(edge_holes) if edge_holes else 0,
        'target_confidence': green_data.confidence
    }
    
    return results

def analyze_bullet_positions(traditional_bullets: List, enhanced_bullets: List) -> Dict[str, Any]:
    """Analyze differences in bullet positions between methods"""
    analysis = {
        'traditional_only': [],
        'enhanced_only': [],
        'common_bullets': [],
        'position_differences': []
    }
    
    # Find bullets detected by traditional method
    for trad_bullet in traditional_bullets:
        trad_center = trad_bullet.center
        found_match = False
        
        for enh_bullet in enhanced_bullets:
            enh_center = enh_bullet.center
            distance = ((trad_center[0] - enh_center[0])**2 + (trad_center[1] - enh_center[1])**2)**0.5
            
            if distance < 30:  # Same threshold used in combining
                analysis['common_bullets'].append({
                    'traditional': {'id': trad_bullet.id, 'center': trad_center, 'confidence': trad_bullet.confidence},
                    'enhanced': {'id': enh_bullet.id, 'center': enh_center, 'confidence': enh_bullet.confidence},
                    'distance': distance
                })
                found_match = True
                break
        
        if not found_match:
            analysis['traditional_only'].append({
                'id': trad_bullet.id,
                'center': trad_center,
                'confidence': trad_bullet.confidence,
                'type': trad_bullet.type
            })
    
    # Find bullets only detected by enhanced method
    for enh_bullet in enhanced_bullets:
        enh_center = enh_bullet.center
        found_match = False
        
        for trad_bullet in traditional_bullets:
            trad_center = trad_bullet.center
            distance = ((trad_center[0] - enh_center[0])**2 + (trad_center[1] - enh_center[1])**2)**0.5
            
            if distance < 30:
                found_match = True
                break
        
        if not found_match:
            analysis['enhanced_only'].append({
                'id': enh_bullet.id,
                'center': enh_center,
                'confidence': enh_bullet.confidence,
                'type': enh_bullet.type
            })
    
    return analysis

def print_comparison_results(traditional: Dict, enhanced: Dict, canny_only: Dict, position_analysis: Dict):
    """Print comprehensive comparison results"""
    print("\n" + "="*80)
    print("🎯 COMPREHENSIVE DETECTION METHOD COMPARISON")
    print("="*80)
    
    # Basic statistics comparison
    print(f"\n📊 DETECTION STATISTICS:")
    print(f"{'Method':<30} {'Total':<8} {'White':<8} {'Black':<8} {'Edge':<8} {'Avg Conf':<10}")
    print("-" * 80)
    print(f"{'Traditional Only':<30} {traditional['total_bullets']:<8} {traditional['white_holes']:<8} {traditional['black_holes']:<8} {traditional['edge_holes']:<8} {traditional['avg_confidence']:<10.3f}")
    print(f"{'Enhanced (Trad+Canny)':<30} {enhanced['total_bullets']:<8} {enhanced['white_holes']:<8} {enhanced['black_holes']:<8} {enhanced['edge_holes']:<8} {enhanced['avg_confidence']:<10.3f}")
    print(f"{'Canny Edge Only':<30} {canny_only['total_bullets']:<8} {canny_only['white_holes']:<8} {canny_only['black_holes']:<8} {canny_only['edge_holes']:<8} {canny_only['avg_confidence']:<10.3f}")
    
    # Performance comparison
    print(f"\n🏆 PERFORMANCE ANALYSIS:")
    improvement = enhanced['total_bullets'] - traditional['total_bullets']
    confidence_change = enhanced['avg_confidence'] - traditional['avg_confidence']
    
    print(f"  Bullet Count Change: {improvement:+d} bullets")
    print(f"  Confidence Change: {confidence_change:+.3f}")
    print(f"  Target Detection: Traditional={traditional['target_confidence']:.3f}, Enhanced={enhanced['target_confidence']:.3f}")
    
    # Position analysis
    print(f"\n🎯 POSITION ANALYSIS:")
    print(f"  Common bullets (both methods): {len(position_analysis['common_bullets'])}")
    print(f"  Traditional only: {len(position_analysis['traditional_only'])}")
    print(f"  Enhanced only: {len(position_analysis['enhanced_only'])}")
    
    if position_analysis['common_bullets']:
        avg_distance = sum(b['distance'] for b in position_analysis['common_bullets']) / len(position_analysis['common_bullets'])
        print(f"  Average position difference: {avg_distance:.1f} pixels")
    
    # Detailed findings
    print(f"\n🔍 DETAILED FINDINGS:")
    
    if position_analysis['enhanced_only']:
        print(f"  ✅ Enhanced method found {len(position_analysis['enhanced_only'])} additional bullets:")
        for bullet in position_analysis['enhanced_only']:
            print(f"     - {bullet['type']} at {bullet['center']} (confidence: {bullet['confidence']:.3f})")
    
    if position_analysis['traditional_only']:
        print(f"  ⚠️  Traditional method found {len(position_analysis['traditional_only'])} bullets not in enhanced:")
        for bullet in position_analysis['traditional_only']:
            print(f"     - {bullet['type']} at {bullet['center']} (confidence: {bullet['confidence']:.3f})")
    
    # Recommendations
    print(f"\n💡 RECOMMENDATIONS:")
    
    if improvement > 0:
        print(f"  ✅ ENHANCED METHOD IS BETTER:")
        print(f"     - Detected {improvement} more bullets")
        print(f"     - {'Higher' if confidence_change > 0 else 'Lower'} average confidence")
        print(f"     - Combines traditional and edge detection strengths")
    elif improvement < 0:
        print(f"  ⚠️  ENHANCED METHOD FILTERED OUT {abs(improvement)} DETECTIONS:")
        print(f"     - May have removed false positives")
        print(f"     - Check if filtered bullets were actually valid")
    else:
        print(f"  ➡️  BOTH METHODS DETECTED SAME NUMBER:")
        print(f"     - Enhanced method confirmed traditional results")
        print(f"     - Edge detection provides validation")
    
    if enhanced['avg_confidence'] > traditional['avg_confidence']:
        print(f"  🎯 Enhanced method has higher confidence scores")
    
    print(f"\n📁 DETAILED RESULTS SAVED:")
    print(f"  - traditional_results.json")
    print(f"  - enhanced_results.json")
    print(f"  - analysis_output/ directory")

def main():
    """Main comparison function"""
    image_path = "Shoot-image4.jpg"
    
    if not os.path.exists(image_path):
        print(f"❌ Image file not found: {image_path}")
        return
    
    print("🎯 DETECTION METHOD COMPARISON ANALYSIS")
    print("="*60)
    print(f"Analyzing: {image_path}")
    print("="*60)
    
    # Run all three detection methods
    traditional_results = run_traditional_detection(image_path)
    enhanced_results = run_enhanced_detection(image_path)
    canny_only_results = run_canny_only_detection(image_path)
    
    if not all([traditional_results['success'], enhanced_results['success'], canny_only_results['success']]):
        print("❌ One or more detection methods failed!")
        return
    
    # Analyze position differences
    position_analysis = analyze_bullet_positions(
        traditional_results['bullets'],
        enhanced_results['bullets']
    )
    
    # Print comprehensive comparison
    print_comparison_results(
        traditional_results,
        enhanced_results,
        canny_only_results,
        position_analysis
    )
    
    # Cleanup
    cleanup_files = ['traditional_results.json', 'enhanced_results.json']
    for file in cleanup_files:
        if os.path.exists(file):
            os.remove(file)

if __name__ == "__main__":
    main()
