import cv2
import numpy as np
import os
import json

class ShootingAnalyzer:
    def __init__(self):
        self.step_images = {}
        self.results = {}
        
    def step1_original_image(self, image_path):
        """Step 1: Load and save original image"""
        print("="*60)
        print("STEP 1: ORIGINAL IMAGE")
        print("="*60)
        
        # Load image
        image = cv2.imread(image_path)
        if image is None:
            print(f"Error: Could not load {image_path}")
            return None
            
        print(f"Image loaded: {image_path}")
        print(f"Image size: {image.shape[1]} x {image.shape[0]} pixels")
        
        # Save original image
        cv2.imwrite("step1_original.jpg", image)
        print("  Saved: step1_original.jpg")
        
        self.step_images['original'] = image
        return image
    
    def step2_green_detection(self, image):
        """Step 2: Detect green targets"""
        print("\n" + "="*60)
        print("STEP 2: GREEN TARGET DETECTION")
        print("="*60)
        
        if image is None:
            print("No image available!")
            return None
        
        # Convert to HSV
        hsv = cv2.cvtColor(image, cv2.COLOR_BGR2HSV)
        
        # Define green color range
        lower_green = np.array([35, 50, 50])
        upper_green = np.array([85, 255, 255])
        
        # Create green mask
        green_mask = cv2.inRange(hsv, lower_green, upper_green)
        
        # Find contours
        contours, _ = cv2.findContours(green_mask, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
        
        # Find largest green area
        largest_contour = None
        max_area = 0
        
        for contour in contours:
            area = cv2.contourArea(contour)
            if area > max_area:
                max_area = area
                largest_contour = contour
        
        if largest_contour is not None:
            # Get bounding rectangle
            x, y, w, h = cv2.boundingRect(largest_contour)
            
            # Draw rectangle on image
            result_image = image.copy()
            cv2.rectangle(result_image, (x, y), (x + w, y + h), (0, 255, 0), 3)
            
            # Add text
            cv2.putText(result_image, f'Green Target: {w}x{h}', (x, y-10), 
                        cv2.FONT_HERSHEY_SIMPLEX, 1, (0, 255, 0), 2)
            
            # Save result
            cv2.imwrite("step2_green_detection.jpg", result_image)
            print("  Saved: step2_green_detection.jpg")
            
            green_data = {
                'bbox': (x, y, w, h),
                'area': max_area,
                'center': (x + w//2, y + h//2)
            }
            
            print(f"Green target detected:")
            print(f"  Position: ({x}, {y})")
            print(f"  Size: {w} x {h} pixels")
            print(f"  Area: {max_area:.0f} pixels")
            
            self.step_images['green_detection'] = result_image
            self.results['green_data'] = green_data
            return green_data
        else:
            print("No green target detected!")
            return None
    
    def step3_crop_green_target(self, image, green_data):
        """Step 3: Crop the green target area"""
        print("\n" + "="*60)
        print("STEP 3: CROP GREEN TARGET")
        print("="*60)
        
        if green_data is None:
            print("No green target data available!")
            return None
        
        x, y, w, h = green_data['bbox']
        
        # Crop the green target area
        cropped_image = image[y:y+h, x:x+w]
        
        print(f"Cropped green target area:")
        print(f"  Original position: ({x}, {y})")
        print(f"  Cropped size: {w} x {h} pixels")
        
        # Save cropped image
        cv2.imwrite("step3_cropped_green.jpg", cropped_image)
        print("  Saved: step3_cropped_green.jpg")
        
        self.step_images['cropped'] = cropped_image
        return cropped_image
    
    def step4_double_size(self, cropped_image):
        """Step 4: Double the size of cropped image"""
        print("\n" + "="*60)
        print("STEP 4: DOUBLE IMAGE SIZE")
        print("="*60)
        
        if cropped_image is None:
            print("No cropped image available!")
            return None
        
        # Get original size
        h, w = cropped_image.shape[:2]
        
        # Double the size
        doubled_image = cv2.resize(cropped_image, (w*2, h*2), interpolation=cv2.INTER_CUBIC)
        
        print(f"Image size doubled:")
        print(f"  Original size: {w} x {h} pixels")
        print(f"  New size: {w*2} x {h*2} pixels")
        
        # Save doubled image
        cv2.imwrite("step4_doubled_size.jpg", doubled_image)
        print("  Saved: step4_doubled_size.jpg")
        
        self.step_images['doubled'] = doubled_image
        return doubled_image
    
    def step5_binary_conversion(self, doubled_image, user_threshold=127):
        """Step 5: Convert to binary image with threshold"""
        print("\n" + "="*60)
        print("STEP 5: BINARY CONVERSION")
        print("="*60)
        
        if doubled_image is None:
            print("No doubled image available!")
            return None
        
        # Convert to grayscale
        gray_image = cv2.cvtColor(doubled_image, cv2.COLOR_BGR2GRAY)
        
        # Apply threshold
        _, binary_image = cv2.threshold(gray_image, user_threshold, 255, cv2.THRESH_BINARY)
        
        # Fill 100 pixels from all sides with black
        binary_image = self.fill_edges_black(binary_image, margin=100)
        
        print(f"Binary conversion with threshold {user_threshold}:")
        print(f"  White pixels: {np.sum(binary_image == 255)}")
        print(f"  Black pixels: {np.sum(binary_image == 0)}")
        
        # Save binary image
        cv2.imwrite("step5_binary.jpg", binary_image)
        print("  Saved: step5_binary.jpg")
        
        # Save grayscale image for reference
        cv2.imwrite("step5_grayscale.jpg", gray_image)
        print("  Saved: step5_grayscale.jpg")
        
        self.step_images['binary'] = binary_image
        self.step_images['grayscale'] = gray_image
        return binary_image, gray_image
    
    def step5_sliding_threshold(self, doubled_image):
        """Step 5b: Create sliding threshold options"""
        print("\n" + "="*60)
        print("STEP 5B: SLIDING THRESHOLD OPTIONS")
        print("="*60)
        
        if doubled_image is None:
            print("No doubled image available!")
            return None
        
        # Convert to grayscale
        gray_image = cv2.cvtColor(doubled_image, cv2.COLOR_BGR2GRAY)
        
        # Test different threshold ranges
        threshold_ranges = [
            (140, 160),
            (130, 170),
            (120, 180),
            (110, 190),
            (100, 200)
        ]
        
        for lower, upper in threshold_ranges:
            print(f"\n" + "="*40)
            print(f"THRESHOLD RANGE: {lower} - {upper}")
            print("="*40)
            
            # Create binary image
            _, binary = cv2.threshold(gray_image, lower, 255, cv2.THRESH_BINARY)
            
            # Fill 100 pixels from all sides with black
            binary = self.fill_edges_black(binary, margin=100)
            
            # Count pixels
            white_pixels = np.sum(binary == 255)
            black_pixels = np.sum(binary == 0)
            total_pixels = white_pixels + black_pixels
            white_percentage = (white_pixels / total_pixels) * 100
            
            print(f"  White pixels: {white_pixels}")
            print(f"  Black pixels: {black_pixels}")
            print(f"  White percentage: {white_percentage:.2f}%")
            
            # Save binary image
            filename = f"step5_sliding_threshold_l{lower}_u{upper}.jpg"
            cv2.imwrite(filename, binary)
            print(f"  Saved: {filename}")
            
            # Also create inverted version
            _, binary_inv = cv2.threshold(gray_image, lower, 255, cv2.THRESH_BINARY_INV)
            binary_inv = self.fill_edges_black(binary_inv, margin=100)
            filename_inv = f"step5_sliding_threshold_inv_l{lower}_u{upper}.jpg"
            cv2.imwrite(filename_inv, binary_inv)
            print(f"  Saved: {filename_inv}")
        
        print("\n" + "="*60)
        print("SLIDING THRESHOLD OPTIONS COMPLETE!")
        print("="*60)
        
        return gray_image
    
    def fill_edges_black(self, image, margin=10):
        """Fill margin pixels from all sides with black"""
        if image is None:
            return None
        
        h, w = image.shape[:2]
        
        # Create a copy of the image
        result = image.copy()
        
        # Fill top margin
        result[:margin, :] = 0
        
        # Fill bottom margin
        result[h-margin:, :] = 0
        
        # Fill left margin
        result[:, :margin] = 0
        
        # Fill right margin
        result[:, w-margin:] = 0
        
        return result
    
    def step6_skip_inversion(self, gray_image):
        """Step 6: Skip inversion - keep original grayscale image"""
        print("\n" + "="*60)
        print("STEP 6: SKIP INVERSION - KEEP ORIGINAL IMAGE")
        print("="*60)
        
        if gray_image is None:
            print("No grayscale image available!")
            return None
        
        print("Inversion step skipped - keeping original grayscale image")
        
        # Save the original grayscale image
        cv2.imwrite("step6_original_grayscale.jpg", gray_image)
        print("  Saved: step6_original_grayscale.jpg")
        
        self.step_images['original_grayscale'] = gray_image
        return gray_image
    
    def step7_detect_bullets(self, grayscale_image, min_area=50, max_area=500):
        """Step 7: Detect bullets as both white holes and black holes using test_edge_filling_margin_100.jpg"""
        print("\n" + "="*60)
        print("STEP 7: BULLET DETECTION (WHITE & BLACK HOLES)")
        print("="*60)
        
        # Load the specific image for bullet detection
        bullet_detection_image = cv2.imread("test_edge_filling_margin_100.jpg", cv2.IMREAD_GRAYSCALE)
        
        if bullet_detection_image is None:
            print("Error: test_edge_filling_margin_100.jpg not found!")
            print("Falling back to original grayscale image...")
            bullet_detection_image = grayscale_image
        
        if bullet_detection_image is None:
            print("No image available for bullet detection!")
            return []
        
        print(f"Using image for bullet detection: {bullet_detection_image.shape}")
        
        bullets = []
        
        # 1. Detect WHITE HOLES (white spots on dark background)
        print("\n--- DETECTING WHITE HOLES ---")
        _, binary_white = cv2.threshold(bullet_detection_image, 127, 255, cv2.THRESH_BINARY)
        
        # Find connected components (white spots)
        num_labels_white, labels_white, stats_white, centroids_white = cv2.connectedComponentsWithStats(binary_white, connectivity=8)
        
        print(f"Found {num_labels_white-1} white connected components")
        
        white_holes = []
        for i in range(1, num_labels_white):  # Skip background (label 0)
            area = stats_white[i, cv2.CC_STAT_AREA]
            x = stats_white[i, cv2.CC_STAT_LEFT]
            y = stats_white[i, cv2.CC_STAT_TOP]
            w = stats_white[i, cv2.CC_STAT_WIDTH]
            h = stats_white[i, cv2.CC_STAT_HEIGHT]
            
            print(f"  White Component {i}: Area = {area}, Position = ({x},{y}), Size = {w}x{h}")
            
            # Mark ALL white holes as bullets (no area filtering)
            white_hole = {
                'id': f"W{i}",
                'area': int(area),
                'position': (int(x), int(y)),
                'size': (int(w), int(h)),
                'center': (int(centroids_white[i][0]), int(centroids_white[i][1])),
                'type': 'white_hole'
            }
            white_holes.append(white_hole)
            bullets.append(white_hole)
        
        # 2. Detect BLACK HOLES (black spots on light background)
        print("\n--- DETECTING BLACK HOLES ---")
        _, binary_black = cv2.threshold(bullet_detection_image, 127, 255, cv2.THRESH_BINARY_INV)
        
        # Find connected components (black spots)
        num_labels_black, labels_black, stats_black, centroids_black = cv2.connectedComponentsWithStats(binary_black, connectivity=8)
        
        print(f"Found {num_labels_black-1} black connected components")
        
        black_holes = []
        for i in range(1, num_labels_black):  # Skip background (label 0)
            area = stats_black[i, cv2.CC_STAT_AREA]
            x = stats_black[i, cv2.CC_STAT_LEFT]
            y = stats_black[i, cv2.CC_STAT_TOP]
            w = stats_black[i, cv2.CC_STAT_WIDTH]
            h = stats_black[i, cv2.CC_STAT_HEIGHT]
            
            print(f"  Black Component {i}: Area = {area}, Position = ({x},{y}), Size = {w}x{h}")
            
            # Mark ALL black holes as bullets (no area filtering)
            black_hole = {
                'id': f"B{i}",
                'area': int(area),
                'position': (int(x), int(y)),
                'size': (int(w), int(h)),
                'center': (int(centroids_black[i][0]), int(centroids_black[i][1])),
                'type': 'black_hole'
            }
            black_holes.append(black_hole)
            bullets.append(black_hole)
        
        # Save visualization with enhanced detection areas
        vis_image = cv2.cvtColor(bullet_detection_image, cv2.COLOR_GRAY2BGR)
        
        # Draw white holes in green with thicker lines and larger text
        for bullet in white_holes:
            x, y = bullet['position']
            w, h = bullet['size']
            # Draw filled rectangle with transparency
            overlay = vis_image.copy()
            cv2.rectangle(overlay, (x, y), (x + w, y + h), (0, 255, 0), -1)  # Filled
            cv2.addWeighted(overlay, 0.3, vis_image, 0.7, 0, vis_image)
            # Draw border
            cv2.rectangle(vis_image, (x, y), (x + w, y + h), (0, 255, 0), 3)
            # Add larger text with background
            text = f"{bullet['id']} ({w}x{h})"
            cv2.putText(vis_image, text, (x, y-10), cv2.FONT_HERSHEY_SIMPLEX, 0.8, (0, 255, 0), 2)
        
        # Draw black holes in red with thicker lines and larger text
        for bullet in black_holes:
            x, y = bullet['position']
            w, h = bullet['size']
            # Draw filled rectangle with transparency
            overlay = vis_image.copy()
            cv2.rectangle(overlay, (x, y), (x + w, y + h), (0, 0, 255), -1)  # Filled
            cv2.addWeighted(overlay, 0.3, vis_image, 0.7, 0, vis_image)
            # Draw border
            cv2.rectangle(vis_image, (x, y), (x + w, y + h), (0, 0, 255), 3)
            # Add larger text with background
            text = f"{bullet['id']} ({w}x{h})"
            cv2.putText(vis_image, text, (x, y-10), cv2.FONT_HERSHEY_SIMPLEX, 0.8, (0, 0, 255), 2)
        
        # Add summary text
        summary_text = f"White Holes: {len(white_holes)}, Black Holes: {len(black_holes)}, Total: {len(bullets)}"
        cv2.putText(vis_image, summary_text, (10, 30), cv2.FONT_HERSHEY_SIMPLEX, 1, (255, 255, 255), 2)
        
        cv2.imwrite("step7_bullet_detection.jpg", vis_image)
        
        # Also create a separate detailed visualization
        detailed_vis = cv2.cvtColor(bullet_detection_image, cv2.COLOR_GRAY2BGR)
        
        # Draw detection areas with different colors and labels
        for i, bullet in enumerate(white_holes):
            x, y = bullet['position']
            w, h = bullet['size']
            cv2.rectangle(detailed_vis, (x, y), (x + w, y + h), (0, 255, 0), 4)
            cv2.putText(detailed_vis, f"WHITE {bullet['id']}", (x, y-15), 
                        cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 255, 0), 2)
            cv2.putText(detailed_vis, f"Area: {bullet['area']}px", (x, y+h+20), 
                        cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 255, 0), 1)
        
        for i, bullet in enumerate(black_holes):
            x, y = bullet['position']
            w, h = bullet['size']
            cv2.rectangle(detailed_vis, (x, y), (x + w, y + h), (0, 0, 255), 4)
            cv2.putText(detailed_vis, f"BLACK {bullet['id']}", (x, y-15), 
                        cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 0, 255), 2)
            cv2.putText(detailed_vis, f"Area: {bullet['area']}px", (x, y+h+20), 
                        cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 0, 255), 1)
        
        cv2.imwrite("step7_detailed_detection.jpg", detailed_vis)
        
        print(f"\nBullet detection results:")
        print(f"  White holes detected: {len(white_holes)} (ALL marked as bullets)")
        print(f"  Black holes detected: {len(black_holes)} (ALL marked as bullets)")
        print(f"  Total bullets: {len(bullets)}")
        print(f"  Note: All detected holes marked as bullets (no area filtering)")
        
        self.results['bullets'] = bullets
        self.results['white_holes'] = white_holes
        self.results['black_holes'] = black_holes
        return bullets
    
    def save_results(self):
        """Save all results to JSON file"""
        print("\n" + "="*60)
        print("SAVING RESULTS")
        print("="*60)
        
        # Convert numpy types to native Python types for JSON serialization
        results_for_json = {}
        
        if 'green_data' in self.results:
            green_data = self.results['green_data']
            results_for_json['green_data'] = {
                'bbox': tuple(int(x) for x in green_data['bbox']),
                'area': float(green_data['area']),
                'center': tuple(int(x) for x in green_data['center'])
            }
        
        if 'center_bbox' in self.results:
            results_for_json['center_bbox'] = tuple(int(x) for x in self.results['center_bbox'])
        
        if 'bullets' in self.results:
            bullets = []
            for bullet in self.results['bullets']:
                bullets.append({
                    'id': bullet['id'],  # Keep as string (W1, B1, etc.)
                    'center': tuple(int(x) for x in bullet['center']),
                    'position': tuple(int(x) for x in bullet['position']),
                    'size': tuple(int(x) for x in bullet['size']),
                    'area': float(bullet['area']),
                    'type': bullet['type']
                })
            results_for_json['bullets'] = bullets
        
        # Save to JSON
        with open("complete_analysis_results.json", "w") as f:
            json.dump(results_for_json, f, indent=2)
        
        print("  Saved: complete_analysis_results.json")
    
    def run_complete_analysis(self, image_path, user_threshold=127, min_area=50, max_area=500, include_sliding=True):
        """Run complete step-by-step analysis"""
        print("COMPLETE SHOOTING ANALYSIS")
        print("="*60)
        print(f"Image: {image_path}")
        print(f"Threshold: {user_threshold}")
        print(f"Bullet area range: {min_area} - {max_area} pixels")
        print(f"Include sliding threshold: {include_sliding}")
        print("="*60)
        
        # Step 1: Original image
        image = self.step1_original_image(image_path)
        if image is None:
            return
        
        # Step 2: Green detection
        green_data = self.step2_green_detection(image)
        if green_data is None:
            return
        
        # Step 3: Crop green target
        cropped_image = self.step3_crop_green_target(image, green_data)
        if cropped_image is None:
            return
        
        # Step 4: Double size
        doubled_image = self.step4_double_size(cropped_image)
        if doubled_image is None:
            return
        
        # Step 5: Binary conversion
        binary_image, gray_image = self.step5_binary_conversion(doubled_image, user_threshold)
        if binary_image is None:
            return
        
        # Step 5b: Sliding threshold options (optional)
        if include_sliding:
            self.step5_sliding_threshold(doubled_image)
        
        # Step 6: Skip inversion - keep original grayscale image
        original_grayscale = self.step6_skip_inversion(gray_image)
        if original_grayscale is None:
            return
        
        # Step 7: Detect bullets (using original grayscale image)
        bullets = self.step7_detect_bullets(original_grayscale, min_area, max_area)
        
        # Save results
        self.save_results()
        
        print("\n" + "="*60)
        print("ANALYSIS COMPLETE!")
        print("="*60)
        print(f"Bullets detected: {len(bullets)}")
        print("\nFiles created:")
        print("  - step1_original.jpg")
        print("  - step2_green_detection.jpg")
        print("  - step3_cropped_green.jpg")
        print("  - step4_doubled_size.jpg")
        print("  - step5_binary.jpg")
        print("  - step5_grayscale.jpg")
        if include_sliding:
            print("  - step5_sliding_threshold_*.jpg (multiple threshold options)")
        print("  - step6_original_grayscale.jpg")
        print("  - step7_bullet_detection.jpg")
        print("  - step7_detailed_detection.jpg")
        print("  - complete_analysis_results.json")

def main():
    """Main function"""
    analyzer = ShootingAnalyzer()
    
    # Analyze Shoot-image4.jpg
    image_path = "Shoot-image4.jpg"
    
    if not os.path.exists(image_path):
        print(f"Error: {image_path} not found!")
        return
    
    # Run complete analysis
    analyzer.run_complete_analysis(
        image_path=image_path,
        user_threshold=127,  # You can change this
        min_area=50,         # Minimum bullet area
        max_area=500         # Maximum bullet area
    )

if __name__ == "__main__":
    main() 