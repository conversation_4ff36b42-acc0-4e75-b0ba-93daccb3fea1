import cv2
import numpy as np
import os
import json
import logging
from typing import Optional, Tuple, List, Dict, Any
from dataclasses import dataclass
from pathlib import Path
import matplotlib
matplotlib.use('TkAgg')  # Use TkAgg backend for better compatibility
import matplotlib
# Set backend before importing pyplot
try:
    matplotlib.use('TkAgg')  # Try TkAgg first (most compatible)
except ImportError:
    try:
        matplotlib.use('Qt5Agg')  # Try Qt5Agg as fallback
    except ImportError:
        matplotlib.use('Agg')  # Use Agg as last resort (no display)

import matplotlib.pyplot as plt
import matplotlib.patches as patches
from matplotlib.figure import Figure

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

@dataclass
class BulletHole:
    """Data class for bullet hole information"""
    id: str
    center: Tuple[int, int]
    position: Tuple[int, int]
    size: Tuple[int, int]
    area: float
    type: str
    confidence: float = 0.0

@dataclass
class TargetData:
    """Data class for target information"""
    bbox: Tuple[int, int, int, int]
    area: float
    center: Tuple[int, int]
    confidence: float = 0.0

class ShootingAnalyzer:
    def __init__(self, config: Optional[Dict[str, Any]] = None):
        """Initialize the analyzer with optional configuration"""
        self.step_images = {}
        self.results = {}
        self.config = self._load_default_config()

        if config:
            self.config.update(config)

        logger.info("ShootingAnalyzer initialized")

    def _load_default_config(self) -> Dict[str, Any]:
        """Load default configuration parameters"""
        return {
            'green_detection': {
                'lower_hsv': [35, 50, 50],
                'upper_hsv': [85, 255, 255],
                'min_area': 1000,  # Minimum area for valid target
                'morphology_kernel_size': 5
            },
            'bullet_detection': {
                'min_area': 10,
                'max_area': 2000,
                'confidence_threshold': 0.5,
                'edge_margin': 100,
                'adaptive_threshold': True
            },
            'image_processing': {
                'resize_factor': 2.0,
                'gaussian_blur_kernel': 3,
                'threshold_value': 127,
                'use_adaptive_threshold': True
            },
            'output': {
                'save_intermediate_steps': True,
                'output_directory': 'analysis_output',
                'image_quality': 95
            },
            'preview': {
                'enable_previews': True,  # Master switch for all previews
                'step1_original': True,   # Show original image
                'step2_green_detection': True,  # Show green target detection
                'step3_cropped': True,    # Show cropped target area
                'step4_doubled': True,    # Show size-doubled image
                'step5_binary': True,     # Show binary conversion
                'step5_sliding': False,   # Show sliding threshold options
                'step6_grayscale': True,  # Show grayscale preparation
                'step7_bullets': True,    # Show bullet detection
                'preview_size': (10, 8),  # Figure size in inches
                'preview_dpi': 100,       # DPI for preview images
                'auto_close_delay': 0.0,  # Auto-close delay in seconds (0 = manual close)
                'save_preview_images': False,  # Save preview plots as images
                'show_titles': True,      # Show titles on preview plots
                'show_grid': False,       # Show grid on preview plots
                'colormap': 'gray'        # Default colormap for grayscale images
            }
        }

    def _check_display_available(self) -> bool:
        """Check if display is available for showing previews"""
        try:
            # Check if we can create a figure
            fig = plt.figure(figsize=(1, 1))
            plt.close(fig)
            return True
        except Exception:
            return False

    def _show_opencv_preview(self, image: np.ndarray, title: str, wait_time: int = 3000) -> bool:
        """Fallback preview using OpenCV when matplotlib fails"""
        try:
            # Ensure image is in correct format for OpenCV
            if len(image.shape) == 3:
                # Color image - ensure it's in BGR format
                display_image = image.copy()
            else:
                # Grayscale image - convert to BGR for consistent display
                display_image = cv2.cvtColor(image, cv2.COLOR_GRAY2BGR)

            # Resize image if too large for display
            height, width = display_image.shape[:2]
            max_display_size = 800
            if max(height, width) > max_display_size:
                scale = max_display_size / max(height, width)
                new_width = int(width * scale)
                new_height = int(height * scale)
                display_image = cv2.resize(display_image, (new_width, new_height))

            # Add title to image
            cv2.putText(display_image, title, (10, 30),
                       cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 255, 255), 2)

            # Add size information
            size_text = f"Size: {width}x{height}"
            cv2.putText(display_image, size_text, (10, display_image.shape[0] - 10),
                       cv2.FONT_HERSHEY_SIMPLEX, 0.5, (255, 255, 255), 1)

            # Show image
            cv2.imshow(title, display_image)
            cv2.waitKey(wait_time)
            cv2.destroyAllWindows()

            return True

        except Exception as e:
            logger.warning(f"OpenCV preview failed for {title}: {str(e)}")
            return False

    def _show_preview(self, image: np.ndarray, title: str, step_key: str,
                     additional_info: str = "", colormap: str = None) -> None:
        """Show image preview if enabled for the specific step"""
        preview_config = self.config['preview']

        # Check if previews are enabled globally and for this specific step
        if not preview_config['enable_previews'] or not preview_config.get(step_key, True):
            return

        # Check if display is available
        if not self._check_display_available():
            logger.warning("Display not available for previews. Saving preview image instead.")
            self._save_preview_only(image, title, step_key, additional_info, colormap)
            return

        try:
            # Clear any existing plots
            plt.clf()
            plt.close('all')

            # Set up matplotlib for interactive display
            plt.ion()  # Turn on interactive mode

            # Create figure with specified size
            fig_size = preview_config['preview_size']
            dpi = preview_config.get('preview_dpi', 100)

            fig, ax = plt.subplots(figsize=fig_size, dpi=dpi)

            # Determine colormap
            if colormap is None:
                colormap = preview_config.get('colormap', 'gray')

            # Display image
            if len(image.shape) == 3:
                # Color image (BGR to RGB conversion for matplotlib)
                image_rgb = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)
                im = ax.imshow(image_rgb)
            else:
                # Grayscale image
                im = ax.imshow(image, cmap=colormap)

            # Configure plot appearance
            if preview_config.get('show_titles', True):
                full_title = f"{title}"
                if additional_info:
                    full_title += f"\n{additional_info}"
                ax.set_title(full_title, fontsize=12, pad=20)

            if not preview_config.get('show_grid', False):
                ax.axis('off')
            else:
                ax.grid(True, alpha=0.3)

            # Add image information as text
            height, width = image.shape[:2]
            info_text = f"Size: {width}×{height}"
            if len(image.shape) == 3:
                info_text += f" (3 channels)"
            else:
                info_text += f" (grayscale)"

            ax.text(0.02, 0.98, info_text, transform=ax.transAxes,
                   bbox=dict(boxstyle="round,pad=0.3", facecolor="white", alpha=0.8),
                   verticalalignment='top', fontsize=10)

            plt.tight_layout()

            # Save preview image if enabled
            if preview_config.get('save_preview_images', False):
                output_dir = self._ensure_output_directory()
                preview_filename = f"preview_{step_key}_{title.replace(' ', '_').lower()}.png"
                preview_path = os.path.join(output_dir, preview_filename)
                plt.savefig(preview_path, dpi=dpi, bbox_inches='tight')
                logger.debug(f"Saved preview: {preview_path}")

            # Force display update
            plt.draw()
            plt.show(block=False)

            # Handle auto-close or manual close
            auto_close_delay = preview_config.get('auto_close_delay', 3.0)
            if auto_close_delay > 0:
                logger.info(f"Showing {title} (auto-close in {auto_close_delay}s)")
                plt.pause(auto_close_delay)
                plt.close(fig)
            else:
                # Manual close - wait for user input
                logger.info(f"Showing {title} - Press Enter to continue...")
                input(f"Press Enter to continue from {title}...")
                plt.close(fig)

        except Exception as e:
            logger.warning(f"Error showing matplotlib preview for {title}: {str(e)}")
            logger.info("Trying OpenCV preview as fallback...")

            # Try OpenCV preview as fallback
            auto_close_delay = preview_config.get('auto_close_delay', 3.0)
            wait_time = int(auto_close_delay * 1000) if auto_close_delay > 0 else 0

            if not self._show_opencv_preview(image, title, wait_time):
                logger.info("OpenCV preview also failed, saving image instead...")
                self._save_preview_only(image, title, step_key, additional_info, colormap)

    def _save_preview_only(self, image: np.ndarray, title: str, step_key: str,
                          additional_info: str = "", colormap: str = None) -> None:
        """Save preview image without displaying (fallback when display unavailable)"""
        try:
            output_dir = self._ensure_output_directory()
            preview_filename = f"preview_{step_key}_{title.replace(' ', '_').lower()}.png"
            preview_path = os.path.join(output_dir, preview_filename)

            # Use OpenCV to save the image
            if len(image.shape) == 3:
                cv2.imwrite(preview_path, image)
            else:
                cv2.imwrite(preview_path, image)

            logger.info(f"Preview saved (display unavailable): {preview_path}")

        except Exception as e:
            logger.error(f"Failed to save preview for {title}: {str(e)}")

    def _show_comparison_preview(self, images: List[Tuple[np.ndarray, str]],
                               main_title: str, step_key: str) -> None:
        """Show multiple images in a comparison view"""
        preview_config = self.config['preview']

        if not preview_config['enable_previews'] or not preview_config.get(step_key, True):
            return

        # Check if display is available
        if not self._check_display_available():
            logger.warning("Display not available for comparison preview. Saving images instead.")
            self._save_comparison_only(images, main_title, step_key)
            return

        try:
            # Clear any existing plots
            plt.clf()
            plt.close('all')
            plt.ion()

            num_images = len(images)
            if num_images == 0:
                return

            # Calculate subplot layout
            cols = min(3, num_images)
            rows = (num_images + cols - 1) // cols

            fig_size = preview_config['preview_size']
            fig, axes = plt.subplots(rows, cols, figsize=(fig_size[0] * cols / 2, fig_size[1] * rows / 2))

            # Handle different subplot configurations
            if num_images == 1:
                axes = [axes]
            elif rows == 1 and cols > 1:
                axes = axes if isinstance(axes, (list, np.ndarray)) else [axes]
            elif rows > 1:
                axes = axes.flatten()
            else:
                axes = [axes]

            for i, (image, subtitle) in enumerate(images):
                ax = axes[i]

                if len(image.shape) == 3:
                    image_rgb = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)
                    ax.imshow(image_rgb)
                else:
                    ax.imshow(image, cmap=preview_config.get('colormap', 'gray'))

                ax.set_title(subtitle, fontsize=10)
                ax.axis('off')

            # Hide unused subplots
            for i in range(num_images, len(axes)):
                axes[i].axis('off')

            if preview_config.get('show_titles', True):
                fig.suptitle(main_title, fontsize=14, y=0.98)

            plt.tight_layout()

            # Save comparison preview if enabled
            if preview_config.get('save_preview_images', False):
                output_dir = self._ensure_output_directory()
                preview_filename = f"preview_comparison_{step_key}_{main_title.replace(' ', '_').lower()}.png"
                preview_path = os.path.join(output_dir, preview_filename)
                plt.savefig(preview_path, dpi=preview_config.get('preview_dpi', 100), bbox_inches='tight')
                logger.debug(f"Saved comparison preview: {preview_path}")

            # Force display update
            plt.draw()
            plt.show(block=False)

            auto_close_delay = preview_config.get('auto_close_delay', 3.0)
            if auto_close_delay > 0:
                logger.info(f"Showing {main_title} (auto-close in {auto_close_delay}s)")
                plt.pause(auto_close_delay)
                plt.close(fig)
            else:
                logger.info(f"Showing {main_title} - Press Enter to continue...")
                input(f"Press Enter to continue from {main_title}...")
                plt.close(fig)

        except Exception as e:
            logger.warning(f"Error showing matplotlib comparison for {main_title}: {str(e)}")
            logger.info("Trying OpenCV comparison preview as fallback...")

            # Try OpenCV comparison as fallback
            if not self._show_opencv_comparison(images, main_title, step_key):
                logger.info("OpenCV comparison also failed, saving images instead...")
                self._save_comparison_only(images, main_title, step_key)

    def _show_opencv_comparison(self, images: List[Tuple[np.ndarray, str]],
                              main_title: str, step_key: str) -> bool:
        """Show comparison using OpenCV windows"""
        try:
            preview_config = self.config['preview']
            auto_close_delay = preview_config.get('auto_close_delay', 3.0)
            wait_time = int(auto_close_delay * 1000) if auto_close_delay > 0 else 0

            logger.info(f"Showing {main_title} using OpenCV (auto-close in {auto_close_delay}s)")

            for i, (image, subtitle) in enumerate(images):
                window_title = f"{main_title} - {subtitle}"
                if not self._show_opencv_preview(image, window_title, wait_time):
                    return False

            return True

        except Exception as e:
            logger.warning(f"OpenCV comparison failed for {main_title}: {str(e)}")
            return False

    def _save_comparison_only(self, images: List[Tuple[np.ndarray, str]],
                            main_title: str, step_key: str) -> None:
        """Save comparison images without displaying (fallback when display unavailable)"""
        try:
            output_dir = self._ensure_output_directory()

            for i, (image, subtitle) in enumerate(images):
                filename = f"preview_{step_key}_{main_title.replace(' ', '_').lower()}_{i+1}_{subtitle.replace(' ', '_').lower()}.png"
                filepath = os.path.join(output_dir, filename)
                cv2.imwrite(filepath, image)
                logger.info(f"Comparison image saved: {filepath}")

        except Exception as e:
            logger.error(f"Failed to save comparison images for {main_title}: {str(e)}")

    def _ensure_output_directory(self):
        """Ensure output directory exists"""
        output_dir = self.config['output']['output_directory']
        os.makedirs(output_dir, exist_ok=True)
        return output_dir

    def _validate_image_path(self, image_path: str) -> bool:
        """Validate image path and format"""
        if not os.path.exists(image_path):
            logger.error(f"Image file not found: {image_path}")
            return False

        # Check file extension
        valid_extensions = {'.jpg', '.jpeg', '.png', '.bmp', '.tiff', '.tif'}
        file_ext = os.path.splitext(image_path)[1].lower()
        if file_ext not in valid_extensions:
            logger.warning(f"Unsupported image format: {file_ext}")

        return True

    def step1_original_image(self, image_path: str) -> Optional[np.ndarray]:
        """Step 1: Load and validate original image with enhanced error handling"""
        logger.info("="*60)
        logger.info("STEP 1: ORIGINAL IMAGE")
        logger.info("="*60)

        try:
            # Validate input
            if not self._validate_image_path(image_path):
                return None

            # Load image
            image = cv2.imread(image_path)
            if image is None:
                logger.error(f"Failed to load image: {image_path}")
                return None

            # Validate image properties
            if len(image.shape) != 3:
                logger.error(f"Invalid image format - expected 3 channels, got {len(image.shape)}")
                return None

            height, width, channels = image.shape
            if width < 100 or height < 100:
                logger.warning(f"Image is very small: {width}x{height}")

            logger.info(f"Image loaded successfully: {image_path}")
            logger.info(f"Image dimensions: {width} x {height} x {channels}")
            logger.info(f"Image size: {os.path.getsize(image_path) / 1024:.1f} KB")

            # Ensure output directory exists
            output_dir = self._ensure_output_directory()

            # Save original image with quality control
            output_path = os.path.join(output_dir, "step1_original.jpg")
            success = cv2.imwrite(output_path, image, [cv2.IMWRITE_JPEG_QUALITY, self.config['output']['image_quality']])

            if success:
                logger.info(f"Saved: {output_path}")
            else:
                logger.error(f"Failed to save: {output_path}")

            self.step_images['original'] = image
            self.results['image_info'] = {
                'path': image_path,
                'width': width,
                'height': height,
                'channels': channels,
                'file_size': os.path.getsize(image_path)
            }

            # Show preview if enabled
            additional_info = f"File: {os.path.basename(image_path)} | Size: {os.path.getsize(image_path) / 1024:.1f} KB"
            self._show_preview(image, "Step 1: Original Image", "step1_original", additional_info)

            return image

        except Exception as e:
            logger.error(f"Error in step1_original_image: {str(e)}")
            return None
    
    def _apply_morphological_operations(self, mask: np.ndarray) -> np.ndarray:
        """Apply morphological operations to clean up the mask"""
        kernel_size = self.config['green_detection']['morphology_kernel_size']
        kernel = cv2.getStructuringElement(cv2.MORPH_ELLIPSE, (kernel_size, kernel_size))

        # Remove noise
        mask = cv2.morphologyEx(mask, cv2.MORPH_OPEN, kernel)
        # Fill holes
        mask = cv2.morphologyEx(mask, cv2.MORPH_CLOSE, kernel)

        return mask

    def _calculate_contour_confidence(self, contour: np.ndarray, area: float) -> float:
        """Calculate confidence score for a contour based on shape properties"""
        try:
            # Calculate shape metrics
            perimeter = cv2.arcLength(contour, True)
            if perimeter == 0:
                return 0.0

            # Circularity (4π*area/perimeter²) - closer to 1 is more circular
            circularity = 4 * np.pi * area / (perimeter * perimeter)

            # Aspect ratio
            x, y, w, h = cv2.boundingRect(contour)
            aspect_ratio = float(w) / h if h > 0 else 0

            # Solidity (area/convex_hull_area)
            hull = cv2.convexHull(contour)
            hull_area = cv2.contourArea(hull)
            solidity = area / hull_area if hull_area > 0 else 0

            # Combine metrics (weights can be adjusted)
            confidence = (circularity * 0.4 +
                         min(aspect_ratio, 1/aspect_ratio) * 0.3 +
                         solidity * 0.3)

            return min(confidence, 1.0)

        except Exception as e:
            logger.warning(f"Error calculating contour confidence: {e}")
            return 0.0

    def step2_green_detection(self, image: np.ndarray) -> Optional[TargetData]:
        """Step 2: Enhanced green target detection with multiple validation methods"""
        logger.info("\n" + "="*60)
        logger.info("STEP 2: ENHANCED GREEN TARGET DETECTION")
        logger.info("="*60)

        if image is None:
            logger.error("No image available for green detection!")
            return None

        try:
            # Convert to HSV for better color detection
            hsv = cv2.cvtColor(image, cv2.COLOR_BGR2HSV)

            # Get color ranges from config
            lower_green = np.array(self.config['green_detection']['lower_hsv'])
            upper_green = np.array(self.config['green_detection']['upper_hsv'])
            min_area = self.config['green_detection']['min_area']

            # Create green mask
            green_mask = cv2.inRange(hsv, lower_green, upper_green)

            # Apply morphological operations to clean up the mask
            green_mask = self._apply_morphological_operations(green_mask)

            # Find contours
            contours, _ = cv2.findContours(green_mask, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)

            if not contours:
                logger.warning("No green contours found!")
                return None

            # Evaluate all contours and find the best candidate
            best_contour = None
            best_score = 0
            best_area = 0

            valid_contours = []

            for contour in contours:
                area = cv2.contourArea(contour)

                # Filter by minimum area
                if area < min_area:
                    continue

                # Calculate confidence score
                confidence = self._calculate_contour_confidence(contour, area)

                # Combined score (area weight + confidence)
                score = area * 0.7 + confidence * area * 0.3

                valid_contours.append({
                    'contour': contour,
                    'area': area,
                    'confidence': confidence,
                    'score': score
                })

                if score > best_score:
                    best_score = score
                    best_contour = contour
                    best_area = area

            if best_contour is None:
                logger.warning(f"No valid green targets found (min area: {min_area})")
                return None

            # Get bounding rectangle
            x, y, w, h = cv2.boundingRect(best_contour)

            # Calculate confidence for the best contour
            confidence = self._calculate_contour_confidence(best_contour, best_area)

            # Create visualization
            result_image = image.copy()

            # Draw all valid contours in light green
            for contour_data in valid_contours:
                cv2.drawContours(result_image, [contour_data['contour']], -1, (0, 200, 0), 1)

            # Draw best contour in bright green
            cv2.drawContours(result_image, [best_contour], -1, (0, 255, 0), 3)
            cv2.rectangle(result_image, (x, y), (x + w, y + h), (0, 255, 0), 2)

            # Add detailed text information
            cv2.putText(result_image, f'Target: {w}x{h} (conf: {confidence:.2f})',
                       (x, y-10), cv2.FONT_HERSHEY_SIMPLEX, 0.8, (0, 255, 0), 2)
            cv2.putText(result_image, f'Area: {best_area:.0f}px',
                       (x, y+h+20), cv2.FONT_HERSHEY_SIMPLEX, 0.6, (0, 255, 0), 2)

            # Save result
            output_dir = self._ensure_output_directory()
            output_path = os.path.join(output_dir, "step2_green_detection.jpg")
            cv2.imwrite(output_path, result_image)
            logger.info(f"Saved: {output_path}")

            # Create target data object
            target_data = TargetData(
                bbox=(x, y, w, h),
                area=best_area,
                center=(x + w//2, y + h//2),
                confidence=confidence
            )

            logger.info(f"Green target detected:")
            logger.info(f"  Position: ({x}, {y})")
            logger.info(f"  Size: {w} x {h} pixels")
            logger.info(f"  Area: {best_area:.0f} pixels")
            logger.info(f"  Confidence: {confidence:.3f}")
            logger.info(f"  Valid contours found: {len(valid_contours)}")

            self.step_images['green_detection'] = result_image
            self.results['green_data'] = target_data

            # Show preview with comparison of original and detection result
            preview_images = [
                (image, "Original Image"),
                (result_image, f"Green Target Detection (Conf: {confidence:.3f})")
            ]
            self._show_comparison_preview(preview_images, "Step 2: Green Target Detection", "step2_green_detection")

            return target_data

        except Exception as e:
            logger.error(f"Error in step2_green_detection: {str(e)}")
            return None
    
    def step3_crop_green_target(self, image: np.ndarray, green_data: TargetData) -> Optional[np.ndarray]:
        """Step 3: Crop the green target area with preview"""
        logger.info("\n" + "="*60)
        logger.info("STEP 3: CROP GREEN TARGET")
        logger.info("="*60)

        if green_data is None:
            logger.error("No green target data available!")
            return None

        try:
            x, y, w, h = green_data.bbox

            # Validate crop boundaries
            img_height, img_width = image.shape[:2]
            if x < 0 or y < 0 or x + w > img_width or y + h > img_height:
                logger.warning("Crop boundaries exceed image dimensions, adjusting...")
                x = max(0, x)
                y = max(0, y)
                w = min(w, img_width - x)
                h = min(h, img_height - y)

            # Crop the green target area
            cropped_image = image[y:y+h, x:x+w]

            logger.info(f"Cropped green target area:")
            logger.info(f"  Original position: ({x}, {y})")
            logger.info(f"  Cropped size: {w} x {h} pixels")

            # Save cropped image
            output_dir = self._ensure_output_directory()
            output_path = os.path.join(output_dir, "step3_cropped_green.jpg")
            cv2.imwrite(output_path, cropped_image)
            logger.info(f"Saved: {output_path}")

            self.step_images['cropped'] = cropped_image

            # Show preview with before/after comparison
            # Create a copy of original with crop area highlighted
            highlighted_image = image.copy()
            cv2.rectangle(highlighted_image, (x, y), (x + w, y + h), (0, 255, 255), 3)
            cv2.putText(highlighted_image, "Crop Area", (x, y-10),
                       cv2.FONT_HERSHEY_SIMPLEX, 1, (0, 255, 255), 2)

            preview_images = [
                (highlighted_image, f"Original with Crop Area ({w}×{h})"),
                (cropped_image, f"Cropped Target Area")
            ]
            additional_info = f"Crop position: ({x}, {y}) | Size: {w}×{h} pixels"
            self._show_comparison_preview(preview_images, "Step 3: Target Cropping", "step3_cropped")

            return cropped_image

        except Exception as e:
            logger.error(f"Error in step3_crop_green_target: {str(e)}")
            return None
    
    def step4_double_size(self, cropped_image: np.ndarray) -> Optional[np.ndarray]:
        """Step 4: Double the size of cropped image with preview"""
        logger.info("\n" + "="*60)
        logger.info("STEP 4: DOUBLE IMAGE SIZE")
        logger.info("="*60)

        if cropped_image is None:
            logger.error("No cropped image available!")
            return None

        try:
            # Get original size
            h, w = cropped_image.shape[:2]
            resize_factor = self.config['image_processing']['resize_factor']

            # Calculate new size
            new_w = int(w * resize_factor)
            new_h = int(h * resize_factor)

            # Resize the image with high-quality interpolation
            doubled_image = cv2.resize(cropped_image, (new_w, new_h), interpolation=cv2.INTER_CUBIC)

            logger.info(f"Image size enhanced:")
            logger.info(f"  Original size: {w} x {h} pixels")
            logger.info(f"  New size: {new_w} x {new_h} pixels")
            logger.info(f"  Resize factor: {resize_factor}x")

            # Save enhanced image
            output_dir = self._ensure_output_directory()
            output_path = os.path.join(output_dir, "step4_doubled_size.jpg")
            cv2.imwrite(output_path, doubled_image)
            logger.info(f"Saved: {output_path}")

            self.step_images['doubled'] = doubled_image

            # Show preview with size comparison
            preview_images = [
                (cropped_image, f"Original ({w}×{h})"),
                (doubled_image, f"Enhanced ({new_w}×{new_h})")
            ]
            self._show_comparison_preview(preview_images, f"Step 4: Image Enhancement ({resize_factor}x)", "step4_doubled")

            return doubled_image

        except Exception as e:
            logger.error(f"Error in step4_double_size: {str(e)}")
            return None
    
    def step5_binary_conversion(self, doubled_image: np.ndarray, user_threshold: int = 127) -> Tuple[Optional[np.ndarray], Optional[np.ndarray]]:
        """Step 5: Convert to binary image with threshold and preview"""
        logger.info("\n" + "="*60)
        logger.info("STEP 5: BINARY CONVERSION")
        logger.info("="*60)

        if doubled_image is None:
            logger.error("No doubled image available!")
            return None, None

        try:
            # Convert to grayscale
            gray_image = cv2.cvtColor(doubled_image, cv2.COLOR_BGR2GRAY)

            # Apply Gaussian blur if configured
            blur_kernel = self.config['image_processing']['gaussian_blur_kernel']
            if blur_kernel > 1:
                gray_image = cv2.GaussianBlur(gray_image, (blur_kernel, blur_kernel), 0)

            # Apply threshold
            if self.config['image_processing']['use_adaptive_threshold']:
                # Use adaptive threshold for better results
                binary_image = cv2.adaptiveThreshold(
                    gray_image, 255, cv2.ADAPTIVE_THRESH_GAUSSIAN_C, cv2.THRESH_BINARY, 11, 2
                )
            else:
                _, binary_image = cv2.threshold(gray_image, user_threshold, 255, cv2.THRESH_BINARY)

            # Fill edges with black (configurable margin)
            edge_margin = self.config['bullet_detection']['edge_margin']
            binary_image = self.fill_edges_black(binary_image, margin=edge_margin)

            # Calculate statistics
            white_pixels = np.sum(binary_image == 255)
            black_pixels = np.sum(binary_image == 0)
            total_pixels = white_pixels + black_pixels
            white_percentage = (white_pixels / total_pixels) * 100 if total_pixels > 0 else 0

            logger.info(f"Binary conversion with threshold {user_threshold}:")
            logger.info(f"  White pixels: {white_pixels} ({white_percentage:.1f}%)")
            logger.info(f"  Black pixels: {black_pixels} ({100-white_percentage:.1f}%)")
            logger.info(f"  Edge margin applied: {edge_margin} pixels")

            # Save images
            output_dir = self._ensure_output_directory()

            binary_path = os.path.join(output_dir, "step5_binary.jpg")
            cv2.imwrite(binary_path, binary_image)
            logger.info(f"Saved: {binary_path}")

            grayscale_path = os.path.join(output_dir, "step5_grayscale.jpg")
            cv2.imwrite(grayscale_path, gray_image)
            logger.info(f"Saved: {grayscale_path}")

            self.step_images['binary'] = binary_image
            self.step_images['grayscale'] = gray_image

            # Show preview with processing steps
            preview_images = [
                (doubled_image, "Enhanced Color Image"),
                (gray_image, "Grayscale Conversion"),
                (binary_image, f"Binary (Threshold: {user_threshold})")
            ]
            self._show_comparison_preview(preview_images, "Step 5: Binary Conversion", "step5_binary")

            return binary_image, gray_image

        except Exception as e:
            logger.error(f"Error in step5_binary_conversion: {str(e)}")
            return None, None
    
    def step5_sliding_threshold(self, doubled_image):
        """Step 5b: Create sliding threshold options"""
        print("\n" + "="*60)
        print("STEP 5B: SLIDING THRESHOLD OPTIONS")
        print("="*60)
        
        if doubled_image is None:
            print("No doubled image available!")
            return None
        
        # Convert to grayscale
        gray_image = cv2.cvtColor(doubled_image, cv2.COLOR_BGR2GRAY)
        
        # Test different threshold ranges
        threshold_ranges = [
            (140, 160),
            (130, 170),
            (120, 180),
            (110, 190),
            (100, 200)
        ]
        
        for lower, upper in threshold_ranges:
            print(f"\n" + "="*40)
            print(f"THRESHOLD RANGE: {lower} - {upper}")
            print("="*40)
            
            # Create binary image
            _, binary = cv2.threshold(gray_image, lower, 255, cv2.THRESH_BINARY)
            
            # Fill 100 pixels from all sides with black
            binary = self.fill_edges_black(binary, margin=100)
            
            # Count pixels
            white_pixels = np.sum(binary == 255)
            black_pixels = np.sum(binary == 0)
            total_pixels = white_pixels + black_pixels
            white_percentage = (white_pixels / total_pixels) * 100
            
            print(f"  White pixels: {white_pixels}")
            print(f"  Black pixels: {black_pixels}")
            print(f"  White percentage: {white_percentage:.2f}%")
            
            # Save binary image
            filename = f"step5_sliding_threshold_l{lower}_u{upper}.jpg"
            cv2.imwrite(filename, binary)
            print(f"  Saved: {filename}")
            
            # Also create inverted version
            _, binary_inv = cv2.threshold(gray_image, lower, 255, cv2.THRESH_BINARY_INV)
            binary_inv = self.fill_edges_black(binary_inv, margin=100)
            filename_inv = f"step5_sliding_threshold_inv_l{lower}_u{upper}.jpg"
            cv2.imwrite(filename_inv, binary_inv)
            print(f"  Saved: {filename_inv}")
        
        logger.info("\n" + "="*60)
        logger.info("SLIDING THRESHOLD OPTIONS COMPLETE!")
        logger.info("="*60)

        # Show preview of different threshold options if enabled
        if self.config['preview']['enable_previews'] and self.config['preview'].get('step5_sliding', False):
            # Create a comparison of different threshold results
            comparison_images = []
            for lower, upper in threshold_ranges[:3]:  # Show first 3 for preview
                _, binary = cv2.threshold(gray_image, lower, 255, cv2.THRESH_BINARY)
                binary = self.fill_edges_black(binary, margin=100)
                comparison_images.append((binary, f"Threshold {lower}-{upper}"))

            self._show_comparison_preview(comparison_images, "Step 5b: Threshold Options", "step5_sliding")

        return gray_image
    
    def fill_edges_black(self, image, margin=10):
        """Fill margin pixels from all sides with black"""
        if image is None:
            return None
        
        h, w = image.shape[:2]
        
        # Create a copy of the image
        result = image.copy()
        
        # Fill top margin
        result[:margin, :] = 0
        
        # Fill bottom margin
        result[h-margin:, :] = 0
        
        # Fill left margin
        result[:, :margin] = 0
        
        # Fill right margin
        result[:, w-margin:] = 0
        
        return result
    
    def step6_skip_inversion(self, gray_image: np.ndarray) -> Optional[np.ndarray]:
        """Step 6: Skip inversion - keep original grayscale image with preview"""
        logger.info("\n" + "="*60)
        logger.info("STEP 6: DETECTION PREPARATION")
        logger.info("="*60)

        if gray_image is None:
            logger.error("No grayscale image available!")
            return None

        try:
            logger.info("Preparing grayscale image for bullet detection")

            # Apply additional preprocessing if needed
            processed_image = gray_image.copy()

            # Optional: Apply additional noise reduction
            if self.config['image_processing']['gaussian_blur_kernel'] > 1:
                kernel_size = self.config['image_processing']['gaussian_blur_kernel']
                processed_image = cv2.GaussianBlur(processed_image, (kernel_size, kernel_size), 0)
                logger.info(f"Applied Gaussian blur with kernel size {kernel_size}")

            # Save the processed grayscale image
            output_dir = self._ensure_output_directory()
            output_path = os.path.join(output_dir, "step6_original_grayscale.jpg")
            cv2.imwrite(output_path, processed_image)
            logger.info(f"Saved: {output_path}")

            self.step_images['original_grayscale'] = processed_image

            # Show preview
            additional_info = f"Ready for bullet detection | Size: {processed_image.shape[1]}×{processed_image.shape[0]}"
            self._show_preview(processed_image, "Step 6: Detection Preparation", "step6_grayscale", additional_info)

            return processed_image

        except Exception as e:
            logger.error(f"Error in step6_skip_inversion: {str(e)}")
            return None
    
    def _calculate_bullet_confidence(self, stats: np.ndarray, area: float) -> float:
        """Calculate confidence score for bullet hole detection"""
        try:
            min_area = self.config['bullet_detection']['min_area']
            max_area = self.config['bullet_detection']['max_area']

            # Area score (prefer medium-sized holes)
            if area < min_area or area > max_area:
                return 0.0

            # Optimal area range (adjust based on your target type)
            optimal_min, optimal_max = 50, 300
            if optimal_min <= area <= optimal_max:
                area_score = 1.0
            else:
                area_score = max(0.3, 1.0 - abs(area - (optimal_min + optimal_max) / 2) / max_area)

            # Aspect ratio score (prefer circular holes)
            w = stats[cv2.CC_STAT_WIDTH]
            h = stats[cv2.CC_STAT_HEIGHT]
            aspect_ratio = min(w, h) / max(w, h) if max(w, h) > 0 else 0
            aspect_score = aspect_ratio  # Closer to 1 is better (more circular)

            # Size consistency score
            size_score = min(w, h) / max(w, h) if max(w, h) > 0 else 0

            # Combined confidence
            confidence = (area_score * 0.5 + aspect_score * 0.3 + size_score * 0.2)
            return min(confidence, 1.0)

        except Exception as e:
            logger.warning(f"Error calculating bullet confidence: {e}")
            return 0.0

    def _apply_adaptive_threshold(self, image: np.ndarray) -> Tuple[np.ndarray, np.ndarray]:
        """Apply adaptive thresholding for better bullet detection"""
        # Standard binary threshold
        _, binary_standard = cv2.threshold(image, 127, 255, cv2.THRESH_BINARY)

        # Adaptive threshold for varying lighting conditions
        binary_adaptive = cv2.adaptiveThreshold(
            image, 255, cv2.ADAPTIVE_THRESH_GAUSSIAN_C, cv2.THRESH_BINARY, 11, 2
        )

        return binary_standard, binary_adaptive

    def step7_detect_bullets(self, grayscale_image: np.ndarray) -> List[BulletHole]:
        """Step 7: Enhanced bullet detection with confidence scoring and multiple methods"""
        logger.info("\n" + "="*60)
        logger.info("STEP 7: ENHANCED BULLET DETECTION")
        logger.info("="*60)

        if grayscale_image is None:
            logger.error("No image available for bullet detection!")
            return []

        try:
            # Use configuration parameters
            min_area = self.config['bullet_detection']['min_area']
            max_area = self.config['bullet_detection']['max_area']
            confidence_threshold = self.config['bullet_detection']['confidence_threshold']
            edge_margin = self.config['bullet_detection']['edge_margin']

            # Apply edge margin (fill edges with neutral value)
            bullet_detection_image = self.fill_edges_black(grayscale_image, margin=edge_margin)

            logger.info(f"Using image for bullet detection: {bullet_detection_image.shape}")
            logger.info(f"Detection parameters: min_area={min_area}, max_area={max_area}")
            logger.info(f"Confidence threshold: {confidence_threshold}")

            bullets = []

            # Apply adaptive thresholding if enabled
            if self.config['bullet_detection']['adaptive_threshold']:
                binary_standard, binary_adaptive = self._apply_adaptive_threshold(bullet_detection_image)
            else:
                _, binary_standard = cv2.threshold(bullet_detection_image, 127, 255, cv2.THRESH_BINARY)
                binary_adaptive = binary_standard

            # 1. Detect WHITE HOLES (white spots on dark background)
            logger.info("\n--- DETECTING WHITE HOLES ---")

            # Find connected components (white spots)
            num_labels_white, _, stats_white, centroids_white = cv2.connectedComponentsWithStats(
                binary_standard, connectivity=8
            )

            logger.info(f"Found {num_labels_white-1} white connected components")

            white_holes = []
            for i in range(1, num_labels_white):  # Skip background (label 0)
                area = stats_white[i, cv2.CC_STAT_AREA]
                x = stats_white[i, cv2.CC_STAT_LEFT]
                y = stats_white[i, cv2.CC_STAT_TOP]
                w = stats_white[i, cv2.CC_STAT_WIDTH]
                h = stats_white[i, cv2.CC_STAT_HEIGHT]

                # Calculate confidence score
                confidence = self._calculate_bullet_confidence(stats_white[i], area)

                logger.debug(f"  White Component {i}: Area={area}, Pos=({x},{y}), Size={w}x{h}, Conf={confidence:.3f}")

                # Filter by area and confidence
                if min_area <= area <= max_area and confidence >= confidence_threshold:
                    white_hole = BulletHole(
                        id=f"W{i}",
                        area=float(area),
                        position=(int(x), int(y)),
                        size=(int(w), int(h)),
                        center=(int(centroids_white[i][0]), int(centroids_white[i][1])),
                        type='white_hole',
                        confidence=confidence
                    )
                    white_holes.append(white_hole)
                    bullets.append(white_hole)
                else:
                    logger.debug(f"    Rejected: area={area} (range: {min_area}-{max_area}), conf={confidence:.3f}")

            logger.info(f"Valid white holes: {len(white_holes)}")
        
            # 2. Detect BLACK HOLES (black spots on light background)
            logger.info("\n--- DETECTING BLACK HOLES ---")
            _, binary_black = cv2.threshold(bullet_detection_image, 127, 255, cv2.THRESH_BINARY_INV)

            # Find connected components (black spots)
            num_labels_black, _, stats_black, centroids_black = cv2.connectedComponentsWithStats(
                binary_black, connectivity=8
            )

            logger.info(f"Found {num_labels_black-1} black connected components")

            black_holes = []
            for i in range(1, num_labels_black):  # Skip background (label 0)
                area = stats_black[i, cv2.CC_STAT_AREA]
                x = stats_black[i, cv2.CC_STAT_LEFT]
                y = stats_black[i, cv2.CC_STAT_TOP]
                w = stats_black[i, cv2.CC_STAT_WIDTH]
                h = stats_black[i, cv2.CC_STAT_HEIGHT]

                # Calculate confidence score
                confidence = self._calculate_bullet_confidence(stats_black[i], area)

                logger.debug(f"  Black Component {i}: Area={area}, Pos=({x},{y}), Size={w}x{h}, Conf={confidence:.3f}")

                # Filter by area and confidence
                if min_area <= area <= max_area and confidence >= confidence_threshold:
                    black_hole = BulletHole(
                        id=f"B{i}",
                        area=float(area),
                        position=(int(x), int(y)),
                        size=(int(w), int(h)),
                        center=(int(centroids_black[i][0]), int(centroids_black[i][1])),
                        type='black_hole',
                        confidence=confidence
                    )
                    black_holes.append(black_hole)
                    bullets.append(black_hole)
                else:
                    logger.debug(f"    Rejected: area={area} (range: {min_area}-{max_area}), conf={confidence:.3f}")

            logger.info(f"Valid black holes: {len(black_holes)}")
        
            # Create enhanced visualization
            vis_image = cv2.cvtColor(bullet_detection_image, cv2.COLOR_GRAY2BGR)

            # Draw white holes in green with confidence-based styling
            for bullet in white_holes:
                x, y = bullet.position
                w, h = bullet.size
                confidence = bullet.confidence

                # Color intensity based on confidence
                color_intensity = int(255 * confidence)
                color = (0, color_intensity, 0)

                # Draw filled rectangle with transparency
                overlay = vis_image.copy()
                cv2.rectangle(overlay, (x, y), (x + w, y + h), color, -1)
                cv2.addWeighted(overlay, 0.3, vis_image, 0.7, 0, vis_image)

                # Draw border (thicker for higher confidence)
                border_thickness = max(1, int(3 * confidence))
                cv2.rectangle(vis_image, (x, y), (x + w, y + h), color, border_thickness)

                # Add text with confidence
                text = f"{bullet.id} ({w}x{h}) {confidence:.2f}"
                cv2.putText(vis_image, text, (x, y-10), cv2.FONT_HERSHEY_SIMPLEX, 0.6, color, 2)

            # Draw black holes in red with confidence-based styling
            for bullet in black_holes:
                x, y = bullet.position
                w, h = bullet.size
                confidence = bullet.confidence

                # Color intensity based on confidence
                color_intensity = int(255 * confidence)
                color = (0, 0, color_intensity)

                # Draw filled rectangle with transparency
                overlay = vis_image.copy()
                cv2.rectangle(overlay, (x, y), (x + w, y + h), color, -1)
                cv2.addWeighted(overlay, 0.3, vis_image, 0.7, 0, vis_image)

                # Draw border (thicker for higher confidence)
                border_thickness = max(1, int(3 * confidence))
                cv2.rectangle(vis_image, (x, y), (x + w, y + h), color, border_thickness)

                # Add text with confidence
                text = f"{bullet.id} ({w}x{h}) {confidence:.2f}"
                cv2.putText(vis_image, text, (x, y-10), cv2.FONT_HERSHEY_SIMPLEX, 0.6, color, 2)

            # Add comprehensive summary
            total_bullets = len(bullets)
            avg_confidence = np.mean([b.confidence for b in bullets]) if bullets else 0

            summary_lines = [
                f"Total Bullets: {total_bullets} (W:{len(white_holes)}, B:{len(black_holes)})",
                f"Avg Confidence: {avg_confidence:.3f}",
                f"Threshold: {confidence_threshold}"
            ]

            for i, line in enumerate(summary_lines):
                y_pos = 30 + i * 25
                cv2.putText(vis_image, line, (10, y_pos), cv2.FONT_HERSHEY_SIMPLEX, 0.7, (255, 255, 255), 2)

            # Save visualization
            output_dir = self._ensure_output_directory()
            vis_path = os.path.join(output_dir, "step7_bullet_detection.jpg")
            cv2.imwrite(vis_path, vis_image)
        
            # Create detailed analysis visualization
            detailed_vis = cv2.cvtColor(bullet_detection_image, cv2.COLOR_GRAY2BGR)

            # Draw detection areas with detailed information
            for bullet in white_holes:
                x, y = bullet.position
                w, h = bullet.size
                confidence = bullet.confidence

                cv2.rectangle(detailed_vis, (x, y), (x + w, y + h), (0, 255, 0), 2)
                cv2.putText(detailed_vis, f"WHITE {bullet.id}", (x, y-25),
                           cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 255, 0), 1)
                cv2.putText(detailed_vis, f"Area: {bullet.area:.0f}px", (x, y-10),
                           cv2.FONT_HERSHEY_SIMPLEX, 0.4, (0, 255, 0), 1)
                cv2.putText(detailed_vis, f"Conf: {confidence:.3f}", (x, y+h+15),
                           cv2.FONT_HERSHEY_SIMPLEX, 0.4, (0, 255, 0), 1)

            for bullet in black_holes:
                x, y = bullet.position
                w, h = bullet.size
                confidence = bullet.confidence

                cv2.rectangle(detailed_vis, (x, y), (x + w, y + h), (0, 0, 255), 2)
                cv2.putText(detailed_vis, f"BLACK {bullet.id}", (x, y-25),
                           cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 0, 255), 1)
                cv2.putText(detailed_vis, f"Area: {bullet.area:.0f}px", (x, y-10),
                           cv2.FONT_HERSHEY_SIMPLEX, 0.4, (0, 0, 255), 1)
                cv2.putText(detailed_vis, f"Conf: {confidence:.3f}", (x, y+h+15),
                           cv2.FONT_HERSHEY_SIMPLEX, 0.4, (0, 0, 255), 1)

            detailed_path = os.path.join(output_dir, "step7_detailed_detection.jpg")
            cv2.imwrite(detailed_path, detailed_vis)

            # Log comprehensive results
            logger.info(f"\nBullet detection results:")
            logger.info(f"  White holes detected: {len(white_holes)} (confidence filtered)")
            logger.info(f"  Black holes detected: {len(black_holes)} (confidence filtered)")
            logger.info(f"  Total valid bullets: {len(bullets)}")
            logger.info(f"  Average confidence: {avg_confidence:.3f}")
            logger.info(f"  Detection parameters used:")
            logger.info(f"    - Min area: {min_area}, Max area: {max_area}")
            logger.info(f"    - Confidence threshold: {confidence_threshold}")
            logger.info(f"    - Edge margin: {edge_margin}")

            # Store results
            self.results['bullets'] = bullets
            self.results['white_holes'] = white_holes
            self.results['black_holes'] = black_holes
            self.results['detection_stats'] = {
                'total_bullets': len(bullets),
                'white_holes': len(white_holes),
                'black_holes': len(black_holes),
                'average_confidence': avg_confidence,
                'parameters': {
                    'min_area': min_area,
                    'max_area': max_area,
                    'confidence_threshold': confidence_threshold,
                    'edge_margin': edge_margin
                }
            }

            # Show comprehensive preview of bullet detection
            preview_images = [
                (bullet_detection_image, "Processed Image"),
                (vis_image, f"Bullet Detection ({len(bullets)} found)"),
                (detailed_vis, "Detailed Analysis")
            ]
            self._show_comparison_preview(preview_images, "Step 7: Bullet Detection Results", "step7_bullets")

            return bullets

        except Exception as e:
            logger.error(f"Error in step7_detect_bullets: {str(e)}")
            return []
    
    def _calculate_shooting_accuracy(self, bullets: List[BulletHole], target_center: Tuple[int, int]) -> Dict[str, Any]:
        """Calculate shooting accuracy metrics"""
        if not bullets:
            return {'accuracy_score': 0.0, 'average_distance': 0.0, 'grouping_size': 0.0}

        # Calculate distances from target center
        distances = []
        for bullet in bullets:
            dx = bullet.center[0] - target_center[0]
            dy = bullet.center[1] - target_center[1]
            distance = np.sqrt(dx*dx + dy*dy)
            distances.append(distance)

        avg_distance = np.mean(distances)
        max_distance = np.max(distances)
        min_distance = np.min(distances)

        # Calculate grouping size (spread of shots)
        if len(bullets) > 1:
            bullet_centers = np.array([bullet.center for bullet in bullets])
            center_of_group = np.mean(bullet_centers, axis=0)
            group_distances = [np.sqrt((center[0] - center_of_group[0])**2 +
                                     (center[1] - center_of_group[1])**2)
                             for center in bullet_centers]
            grouping_size = np.max(group_distances) * 2  # Diameter of group
        else:
            grouping_size = 0.0

        # Calculate accuracy score (inverse of average distance, normalized)
        accuracy_score = max(0.0, 1.0 - (avg_distance / 200.0))  # Adjust 200 based on target size

        return {
            'accuracy_score': accuracy_score,
            'average_distance': avg_distance,
            'max_distance': max_distance,
            'min_distance': min_distance,
            'grouping_size': grouping_size,
            'shot_count': len(bullets)
        }

    def save_results(self):
        """Save comprehensive results to JSON file with enhanced metadata"""
        logger.info("\n" + "="*60)
        logger.info("SAVING COMPREHENSIVE RESULTS")
        logger.info("="*60)

        try:
            # Convert results to JSON-serializable format
            results_for_json = {
                'analysis_metadata': {
                    'timestamp': str(np.datetime64('now')),
                    'version': '2.0',
                    'configuration': self.config
                }
            }

            # Image information
            if 'image_info' in self.results:
                results_for_json['image_info'] = self.results['image_info']

            # Green target data
            if 'green_data' in self.results:
                green_data = self.results['green_data']
                results_for_json['green_data'] = {
                    'bbox': green_data.bbox,
                    'area': green_data.area,
                    'center': green_data.center,
                    'confidence': green_data.confidence
                }

            # Bullet detection results
            if 'bullets' in self.results:
                bullets_data = []
                for bullet in self.results['bullets']:
                    bullets_data.append({
                        'id': bullet.id,
                        'center': bullet.center,
                        'position': bullet.position,
                        'size': bullet.size,
                        'area': bullet.area,
                        'type': bullet.type,
                        'confidence': bullet.confidence
                    })
                results_for_json['bullets'] = bullets_data

                # Calculate accuracy metrics if we have target center
                if 'green_data' in self.results:
                    target_center = self.results['green_data'].center
                    accuracy_metrics = self._calculate_shooting_accuracy(self.results['bullets'], target_center)
                    results_for_json['accuracy_metrics'] = accuracy_metrics

            # Detection statistics
            if 'detection_stats' in self.results:
                results_for_json['detection_stats'] = self.results['detection_stats']

            # Save to JSON with timestamp
            output_dir = self._ensure_output_directory()
            output_path = os.path.join(output_dir, "complete_analysis_results.json")

            with open(output_path, "w") as f:
                json.dump(results_for_json, f, indent=2, default=str)

            logger.info(f"Saved comprehensive results: {output_path}")

            # Also save a summary report
            self._save_summary_report(results_for_json, output_dir)

        except Exception as e:
            logger.error(f"Error saving results: {str(e)}")

    def _save_summary_report(self, results: Dict[str, Any], output_dir: str):
        """Save a human-readable summary report"""
        try:
            report_path = os.path.join(output_dir, "analysis_summary.txt")

            with open(report_path, "w") as f:
                f.write("SHOOTING ANALYSIS SUMMARY REPORT\n")
                f.write("=" * 50 + "\n\n")

                # Image info
                if 'image_info' in results:
                    img_info = results['image_info']
                    f.write(f"Image: {img_info['path']}\n")
                    f.write(f"Dimensions: {img_info['width']} x {img_info['height']}\n")
                    f.write(f"File size: {img_info['file_size'] / 1024:.1f} KB\n\n")

                # Target detection
                if 'green_data' in results:
                    green = results['green_data']
                    f.write(f"Target Detection:\n")
                    f.write(f"  Position: {green['bbox'][:2]}\n")
                    f.write(f"  Size: {green['bbox'][2:]} pixels\n")
                    f.write(f"  Confidence: {green['confidence']:.3f}\n\n")

                # Bullet detection
                if 'bullets' in results:
                    bullets = results['bullets']
                    f.write(f"Bullet Detection:\n")
                    f.write(f"  Total bullets found: {len(bullets)}\n")

                    white_bullets = [b for b in bullets if b['type'] == 'white_hole']
                    black_bullets = [b for b in bullets if b['type'] == 'black_hole']

                    f.write(f"  White holes: {len(white_bullets)}\n")
                    f.write(f"  Black holes: {len(black_bullets)}\n")

                    if bullets:
                        avg_conf = np.mean([b['confidence'] for b in bullets])
                        f.write(f"  Average confidence: {avg_conf:.3f}\n")

                # Accuracy metrics
                if 'accuracy_metrics' in results:
                    acc = results['accuracy_metrics']
                    f.write(f"\nAccuracy Analysis:\n")
                    f.write(f"  Accuracy score: {acc['accuracy_score']:.3f}\n")
                    f.write(f"  Average distance from center: {acc['average_distance']:.1f} pixels\n")
                    f.write(f"  Shot grouping size: {acc['grouping_size']:.1f} pixels\n")
                    f.write(f"  Best shot distance: {acc['min_distance']:.1f} pixels\n")
                    f.write(f"  Worst shot distance: {acc['max_distance']:.1f} pixels\n")

            logger.info(f"Saved summary report: {report_path}")

        except Exception as e:
            logger.error(f"Error saving summary report: {str(e)}")
    
    def run_complete_analysis(self, image_path: str, custom_config: Optional[Dict[str, Any]] = None) -> bool:
        """Run complete step-by-step analysis with enhanced error handling and configuration"""
        logger.info("COMPLETE SHOOTING ANALYSIS v2.0")
        logger.info("="*60)
        logger.info(f"Image: {image_path}")

        # Update configuration if provided
        if custom_config:
            self.config.update(custom_config)
            logger.info("Custom configuration applied")

        logger.info(f"Configuration:")
        logger.info(f"  Green detection: {self.config['green_detection']}")
        logger.info(f"  Bullet detection: {self.config['bullet_detection']}")
        logger.info(f"  Image processing: {self.config['image_processing']}")
        logger.info("="*60)

        try:
            # Step 1: Load and validate original image
            logger.info("\n🔍 Starting Step 1: Image Loading")
            image = self.step1_original_image(image_path)
            if image is None:
                logger.error("Failed to load image. Analysis terminated.")
                return False

            # Step 2: Enhanced green target detection
            logger.info("\n🎯 Starting Step 2: Target Detection")
            green_data = self.step2_green_detection(image)
            if green_data is None:
                logger.error("Failed to detect green target. Analysis terminated.")
                return False

            # Step 3: Crop green target area
            logger.info("\n✂️ Starting Step 3: Target Cropping")
            cropped_image = self.step3_crop_green_target(image, green_data)
            if cropped_image is None:
                logger.error("Failed to crop target area. Analysis terminated.")
                return False

            # Step 4: Enhance image resolution
            logger.info("\n🔍 Starting Step 4: Image Enhancement")
            doubled_image = self.step4_double_size(cropped_image)
            if doubled_image is None:
                logger.error("Failed to enhance image. Analysis terminated.")
                return False

            # Step 5: Image preprocessing
            logger.info("\n⚙️ Starting Step 5: Image Preprocessing")
            threshold_value = self.config['image_processing']['threshold_value']
            binary_image, gray_image = self.step5_binary_conversion(doubled_image, threshold_value)
            if binary_image is None:
                logger.error("Failed to preprocess image. Analysis terminated.")
                return False

            # Step 5b: Optional sliding threshold analysis
            if self.config['output']['save_intermediate_steps']:
                logger.info("\n📊 Starting Step 5b: Threshold Analysis")
                self.step5_sliding_threshold(doubled_image)

            # Step 6: Prepare for bullet detection
            logger.info("\n🔧 Starting Step 6: Detection Preparation")
            original_grayscale = self.step6_skip_inversion(gray_image)
            if original_grayscale is None:
                logger.error("Failed to prepare for detection. Analysis terminated.")
                return False

            # Step 7: Enhanced bullet detection
            logger.info("\n🎯 Starting Step 7: Bullet Detection")
            bullets = self.step7_detect_bullets(original_grayscale)

            # Save comprehensive results
            logger.info("\n💾 Saving Results")
            self.save_results()

            # Final summary
            logger.info("\n" + "="*60)
            logger.info("✅ ANALYSIS COMPLETE!")
            logger.info("="*60)
            logger.info(f"📊 Results Summary:")
            logger.info(f"  • Target detected: ✅ (confidence: {green_data.confidence:.3f})")
            logger.info(f"  • Bullets detected: {len(bullets)}")

            if bullets:
                avg_confidence = np.mean([b.confidence for b in bullets])
                logger.info(f"  • Average bullet confidence: {avg_confidence:.3f}")

                # Calculate and display accuracy if possible
                if 'accuracy_metrics' in self.results:
                    acc = self.results['accuracy_metrics']
                    logger.info(f"  • Accuracy score: {acc['accuracy_score']:.3f}")
                    logger.info(f"  • Shot grouping: {acc['grouping_size']:.1f} pixels")

            output_dir = self.config['output']['output_directory']
            logger.info(f"\n📁 Output files saved to: {output_dir}/")
            logger.info(f"  • Analysis results: complete_analysis_results.json")
            logger.info(f"  • Summary report: analysis_summary.txt")
            logger.info(f"  • Visualization images: step*.jpg")

            return True

        except Exception as e:
            logger.error(f"Critical error during analysis: {str(e)}")
            logger.error("Analysis terminated due to unexpected error.")
            return False

    def get_analysis_summary(self) -> Dict[str, Any]:
        """Get a summary of the current analysis results"""
        summary = {
            'has_results': bool(self.results),
            'target_detected': 'green_data' in self.results,
            'bullets_detected': 'bullets' in self.results,
            'bullet_count': len(self.results.get('bullets', [])),
            'configuration': self.config
        }

        if 'green_data' in self.results:
            summary['target_confidence'] = self.results['green_data'].confidence

        if 'bullets' in self.results:
            bullets = self.results['bullets']
            if bullets:
                summary['average_bullet_confidence'] = np.mean([b.confidence for b in bullets])
                summary['bullet_types'] = {
                    'white_holes': len([b for b in bullets if b.type == 'white_hole']),
                    'black_holes': len([b for b in bullets if b.type == 'black_hole'])
                }

        return summary

    def enable_all_previews(self) -> None:
        """Enable all preview options"""
        preview_config = self.config['preview']
        preview_config['enable_previews'] = True
        for key in preview_config:
            if key.startswith('step') and isinstance(preview_config[key], bool):
                preview_config[key] = True
        logger.info("All previews enabled")

    def disable_all_previews(self) -> None:
        """Disable all preview options"""
        self.config['preview']['enable_previews'] = False
        logger.info("All previews disabled")

    def set_preview_mode(self, mode: str) -> None:
        """Set preview mode: 'all', 'none', 'key_steps', 'final_only'"""
        preview_config = self.config['preview']

        if mode == 'all':
            self.enable_all_previews()
        elif mode == 'none':
            self.disable_all_previews()
        elif mode == 'key_steps':
            preview_config['enable_previews'] = True
            preview_config['step1_original'] = True
            preview_config['step2_green_detection'] = True
            preview_config['step3_cropped'] = False
            preview_config['step4_doubled'] = False
            preview_config['step5_binary'] = True
            preview_config['step5_sliding'] = False
            preview_config['step6_grayscale'] = False
            preview_config['step7_bullets'] = True
            logger.info("Key steps preview mode enabled")
        elif mode == 'final_only':
            preview_config['enable_previews'] = True
            for key in preview_config:
                if key.startswith('step'):
                    preview_config[key] = False
            preview_config['step7_bullets'] = True
            logger.info("Final results only preview mode enabled")
        else:
            logger.warning(f"Unknown preview mode: {mode}")

    def set_auto_close_delay(self, delay: float) -> None:
        """Set auto-close delay for previews (0 = manual close)"""
        self.config['preview']['auto_close_delay'] = delay
        if delay > 0:
            logger.info(f"Preview auto-close delay set to {delay} seconds")
        else:
            logger.info("Preview auto-close disabled (manual close)")

    def export_results_csv(self, filename: Optional[str] = None) -> str:
        """Export bullet detection results to CSV format"""
        if 'bullets' not in self.results:
            logger.warning("No bullet detection results to export")
            return ""

        if filename is None:
            output_dir = self._ensure_output_directory()
            filename = os.path.join(output_dir, "bullet_detection_results.csv")

        try:
            import csv

            with open(filename, 'w', newline='') as csvfile:
                fieldnames = ['id', 'type', 'center_x', 'center_y', 'position_x', 'position_y',
                             'width', 'height', 'area', 'confidence']
                writer = csv.DictWriter(csvfile, fieldnames=fieldnames)

                writer.writeheader()
                for bullet in self.results['bullets']:
                    writer.writerow({
                        'id': bullet.id,
                        'type': bullet.type,
                        'center_x': bullet.center[0],
                        'center_y': bullet.center[1],
                        'position_x': bullet.position[0],
                        'position_y': bullet.position[1],
                        'width': bullet.size[0],
                        'height': bullet.size[1],
                        'area': bullet.area,
                        'confidence': bullet.confidence
                    })

            logger.info(f"Exported bullet results to CSV: {filename}")
            return filename

        except Exception as e:
            logger.error(f"Error exporting to CSV: {str(e)}")
            return ""

def create_sample_config_file(filename: str = "analyzer_config.json") -> None:
    """Create a sample configuration file"""
    sample_config = {
        "green_detection": {
            "lower_hsv": [35, 50, 50],
            "upper_hsv": [85, 255, 255],
            "min_area": 1000,
            "morphology_kernel_size": 5
        },
        "bullet_detection": {
            "min_area": 10,
            "max_area": 2000,
            "confidence_threshold": 0.5,
            "edge_margin": 100,
            "adaptive_threshold": True
        },
        "image_processing": {
            "resize_factor": 2.0,
            "gaussian_blur_kernel": 3,
            "threshold_value": 127,
            "use_adaptive_threshold": True
        },
        "preview": {
            "enable_previews": True,
            "step1_original": True,
            "step2_green_detection": True,
            "step3_cropped": True,
            "step4_doubled": True,
            "step5_binary": True,
            "step5_sliding": False,
            "step6_grayscale": True,
            "step7_bullets": True,
            "preview_size": [10, 8],
            "auto_close_delay": 0.0,
            "save_preview_images": False
        },
        "output": {
            "save_intermediate_steps": True,
            "output_directory": "analysis_output",
            "image_quality": 95
        }
    }

    try:
        with open(filename, 'w') as f:
            json.dump(sample_config, f, indent=2)
        logger.info(f"Sample configuration file created: {filename}")
    except Exception as e:
        logger.error(f"Failed to create config file: {e}")

def create_analyzer_with_config(config_file: Optional[str] = None) -> ShootingAnalyzer:
    """Factory function to create analyzer with optional config file"""
    config = None

    if config_file and os.path.exists(config_file):
        try:
            with open(config_file, 'r') as f:
                config = json.load(f)
            logger.info(f"Loaded configuration from: {config_file}")
        except Exception as e:
            logger.warning(f"Failed to load config file {config_file}: {e}")

    return ShootingAnalyzer(config)

def main():
    """Enhanced main function with preview controls and better configuration"""
    import sys

    # Set up logging level based on environment
    if '--debug' in sys.argv:
        logging.getLogger().setLevel(logging.DEBUG)
        logger.info("Debug logging enabled")

    # Create analyzer with optional config
    config_file = "analyzer_config.json" if os.path.exists("analyzer_config.json") else None
    analyzer = create_analyzer_with_config(config_file)

    # Handle preview mode arguments
    if '--no-preview' in sys.argv:
        analyzer.set_preview_mode('none')
    elif '--preview-key' in sys.argv:
        analyzer.set_preview_mode('key_steps')
    elif '--preview-final' in sys.argv:
        analyzer.set_preview_mode('final_only')
    elif '--preview-all' in sys.argv:
        analyzer.set_preview_mode('all')

    # Handle auto-close delay
    if '--auto-close' in sys.argv:
        try:
            delay_idx = sys.argv.index('--auto-close') + 1
            if delay_idx < len(sys.argv):
                delay = float(sys.argv[delay_idx])
                analyzer.set_auto_close_delay(delay)
            else:
                analyzer.set_auto_close_delay(3.0)  # Default 3 seconds when --auto-close is specified
        except (ValueError, IndexError):
            analyzer.set_auto_close_delay(3.0)
    elif '--manual-close' in sys.argv:
        analyzer.set_auto_close_delay(0.0)
    # If neither flag is specified, use the default from config (which is now 0.0 = manual)

    # Default image path
    image_path = "Shoot-image4.jpg"

    # Check for command line arguments
    for arg in sys.argv[1:]:
        if not arg.startswith('--') and os.path.exists(arg):
            image_path = arg
            break

    if not os.path.exists(image_path):
        logger.error(f"Image file not found: {image_path}")
        logger.info("Available images in current directory:")
        for file in os.listdir('.'):
            if file.lower().endswith(('.jpg', '.jpeg', '.png', '.bmp')):
                logger.info(f"  - {file}")
        logger.info("\nUsage examples:")
        logger.info("  python complete_shooting_analyzer.py image.jpg")
        logger.info("  python complete_shooting_analyzer.py image.jpg --preview-key")
        logger.info("  python complete_shooting_analyzer.py image.jpg --no-preview")
        logger.info("  python complete_shooting_analyzer.py image.jpg --auto-close 3")
        return

    # Optional custom configuration for this run
    custom_config = None
    if '--high-precision' in sys.argv:
        custom_config = {
            'bullet_detection': {
                'confidence_threshold': 0.7,
                'min_area': 20,
                'max_area': 1000
            }
        }
        logger.info("High precision mode enabled")

    # Display current preview settings
    preview_config = analyzer.config['preview']
    if preview_config['enable_previews']:
        enabled_steps = [key for key, value in preview_config.items()
                        if key.startswith('step') and value]
        logger.info(f"Preview enabled for: {', '.join(enabled_steps)}")
        if preview_config['auto_close_delay'] > 0:
            logger.info(f"Auto-close delay: {preview_config['auto_close_delay']} seconds")
        else:
            logger.info("Manual close mode (press Enter to continue)")
    else:
        logger.info("Previews disabled")

    # Run analysis
    logger.info(f"Starting analysis of: {image_path}")
    success = analyzer.run_complete_analysis(image_path, custom_config)

    if success:
        # Print summary
        summary = analyzer.get_analysis_summary()
        logger.info("\n📋 Analysis Summary:")
        logger.info(f"  Target detected: {'✅' if summary['target_detected'] else '❌'}")
        logger.info(f"  Bullets found: {summary['bullet_count']}")

        if summary['bullet_count'] > 0:
            # Export to CSV
            csv_file = analyzer.export_results_csv()
            if csv_file:
                logger.info(f"  CSV export: {csv_file}")

        logger.info("\n🎯 Analysis completed successfully!")
    else:
        logger.error("❌ Analysis failed!")
        sys.exit(1)

def print_help():
    """Print help information"""
    help_text = """
🎯 Complete Shooting Analyzer v2.0 - Enhanced with Live Previews

USAGE:
    python complete_shooting_analyzer.py [IMAGE_PATH] [OPTIONS]

ARGUMENTS:
    IMAGE_PATH          Path to the shooting target image (default: Shoot-image4.jpg)

PREVIEW OPTIONS:
    --preview-all       Enable previews for all steps (default)
    --preview-key       Enable previews for key steps only (1,2,5,7)
    --preview-final     Enable preview for final results only (step 7)
    --no-preview        Disable all previews for faster processing

    --auto-close [SEC]  Auto-close previews after SEC seconds (default: 3 when specified)
    --manual-close      Wait for Enter key to close each preview (default behavior)

ANALYSIS OPTIONS:
    --high-precision    Use high precision detection settings
    --debug             Enable debug logging
    --create-config     Create sample configuration file (analyzer_config.json)

EXAMPLES:
    # Basic analysis with manual previews (default - press Enter to continue)
    python complete_shooting_analyzer.py my_target.jpg

    # Fast analysis without previews
    python complete_shooting_analyzer.py my_target.jpg --no-preview

    # Key steps only with 5-second auto-close
    python complete_shooting_analyzer.py my_target.jpg --preview-key --auto-close 5

    # Auto-close all previews after 3 seconds
    python complete_shooting_analyzer.py my_target.jpg --auto-close 3

    # High precision analysis with final results only
    python complete_shooting_analyzer.py my_target.jpg --high-precision --preview-final

PREVIEW FEATURES:
    ✅ Live image display at each processing step
    ✅ Before/after comparisons
    ✅ Confidence scores and detection metrics
    ✅ Configurable auto-close or manual control
    ✅ Save preview images to output directory
    ✅ Interactive matplotlib windows with zoom/pan

OUTPUT FILES:
    📁 analysis_output/
    ├── step1_original.jpg              # Original image
    ├── step2_green_detection.jpg       # Target detection
    ├── step3_cropped_green.jpg         # Cropped target
    ├── step4_doubled_size.jpg          # Enhanced resolution
    ├── step5_binary.jpg                # Binary conversion
    ├── step6_original_grayscale.jpg    # Detection preparation
    ├── step7_bullet_detection.jpg      # Final results
    ├── complete_analysis_results.json  # Detailed data
    ├── analysis_summary.txt            # Human-readable report
    └── bullet_detection_results.csv    # Spreadsheet data
"""
    print(help_text)

if __name__ == "__main__":
    import sys
    if '--help' in sys.argv or '-h' in sys.argv:
        print_help()
    elif '--create-config' in sys.argv:
        create_sample_config_file()
        print("✅ Sample configuration file created: analyzer_config.json")
        print("📝 Edit this file to customize analysis parameters")
        print("🚀 Run analysis with: python complete_shooting_analyzer.py your_image.jpg")
    else:
        main()