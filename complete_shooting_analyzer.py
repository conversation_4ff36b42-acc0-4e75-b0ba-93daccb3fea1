import cv2
import numpy as np
import os
import json
import logging
from typing import Optional, Tuple, List, Dict, Any
from dataclasses import dataclass
from pathlib import Path

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

@dataclass
class BulletHole:
    """Data class for bullet hole information"""
    id: str
    center: Tuple[int, int]
    position: Tuple[int, int]
    size: Tuple[int, int]
    area: float
    type: str
    confidence: float = 0.0

@dataclass
class TargetData:
    """Data class for target information"""
    bbox: Tuple[int, int, int, int]
    area: float
    center: Tuple[int, int]
    confidence: float = 0.0

class ShootingAnalyzer:
    def __init__(self, config: Optional[Dict[str, Any]] = None):
        """Initialize the analyzer with optional configuration"""
        self.step_images = {}
        self.results = {}
        self.config = self._load_default_config()

        if config:
            self.config.update(config)

        logger.info("ShootingAnalyzer initialized")

    def _load_default_config(self) -> Dict[str, Any]:
        """Load default configuration parameters"""
        return {
            'green_detection': {
                'lower_hsv': [35, 50, 50],
                'upper_hsv': [85, 255, 255],
                'min_area': 1000,  # Minimum area for valid target
                'morphology_kernel_size': 5
            },
            'bullet_detection': {
                'min_area': 10,
                'max_area': 2000,
                'confidence_threshold': 0.5,
                'edge_margin': 100,
                'adaptive_threshold': True
            },
            'image_processing': {
                'resize_factor': 2.0,
                'gaussian_blur_kernel': 3,
                'threshold_value': 127,
                'use_adaptive_threshold': True
            },
            'output': {
                'save_intermediate_steps': True,
                'output_directory': 'analysis_output',
                'image_quality': 95
            }
        }
        
    def _ensure_output_directory(self):
        """Ensure output directory exists"""
        output_dir = self.config['output']['output_directory']
        os.makedirs(output_dir, exist_ok=True)
        return output_dir

    def _validate_image_path(self, image_path: str) -> bool:
        """Validate image path and format"""
        if not os.path.exists(image_path):
            logger.error(f"Image file not found: {image_path}")
            return False

        # Check file extension
        valid_extensions = {'.jpg', '.jpeg', '.png', '.bmp', '.tiff', '.tif'}
        file_ext = os.path.splitext(image_path)[1].lower()
        if file_ext not in valid_extensions:
            logger.warning(f"Unsupported image format: {file_ext}")

        return True

    def step1_original_image(self, image_path: str) -> Optional[np.ndarray]:
        """Step 1: Load and validate original image with enhanced error handling"""
        logger.info("="*60)
        logger.info("STEP 1: ORIGINAL IMAGE")
        logger.info("="*60)

        try:
            # Validate input
            if not self._validate_image_path(image_path):
                return None

            # Load image
            image = cv2.imread(image_path)
            if image is None:
                logger.error(f"Failed to load image: {image_path}")
                return None

            # Validate image properties
            if len(image.shape) != 3:
                logger.error(f"Invalid image format - expected 3 channels, got {len(image.shape)}")
                return None

            height, width, channels = image.shape
            if width < 100 or height < 100:
                logger.warning(f"Image is very small: {width}x{height}")

            logger.info(f"Image loaded successfully: {image_path}")
            logger.info(f"Image dimensions: {width} x {height} x {channels}")
            logger.info(f"Image size: {os.path.getsize(image_path) / 1024:.1f} KB")

            # Ensure output directory exists
            output_dir = self._ensure_output_directory()

            # Save original image with quality control
            output_path = os.path.join(output_dir, "step1_original.jpg")
            success = cv2.imwrite(output_path, image, [cv2.IMWRITE_JPEG_QUALITY, self.config['output']['image_quality']])

            if success:
                logger.info(f"Saved: {output_path}")
            else:
                logger.error(f"Failed to save: {output_path}")

            self.step_images['original'] = image
            self.results['image_info'] = {
                'path': image_path,
                'width': width,
                'height': height,
                'channels': channels,
                'file_size': os.path.getsize(image_path)
            }

            return image

        except Exception as e:
            logger.error(f"Error in step1_original_image: {str(e)}")
            return None
    
    def _apply_morphological_operations(self, mask: np.ndarray) -> np.ndarray:
        """Apply morphological operations to clean up the mask"""
        kernel_size = self.config['green_detection']['morphology_kernel_size']
        kernel = cv2.getStructuringElement(cv2.MORPH_ELLIPSE, (kernel_size, kernel_size))

        # Remove noise
        mask = cv2.morphologyEx(mask, cv2.MORPH_OPEN, kernel)
        # Fill holes
        mask = cv2.morphologyEx(mask, cv2.MORPH_CLOSE, kernel)

        return mask

    def _calculate_contour_confidence(self, contour: np.ndarray, area: float) -> float:
        """Calculate confidence score for a contour based on shape properties"""
        try:
            # Calculate shape metrics
            perimeter = cv2.arcLength(contour, True)
            if perimeter == 0:
                return 0.0

            # Circularity (4π*area/perimeter²) - closer to 1 is more circular
            circularity = 4 * np.pi * area / (perimeter * perimeter)

            # Aspect ratio
            x, y, w, h = cv2.boundingRect(contour)
            aspect_ratio = float(w) / h if h > 0 else 0

            # Solidity (area/convex_hull_area)
            hull = cv2.convexHull(contour)
            hull_area = cv2.contourArea(hull)
            solidity = area / hull_area if hull_area > 0 else 0

            # Combine metrics (weights can be adjusted)
            confidence = (circularity * 0.4 +
                         min(aspect_ratio, 1/aspect_ratio) * 0.3 +
                         solidity * 0.3)

            return min(confidence, 1.0)

        except Exception as e:
            logger.warning(f"Error calculating contour confidence: {e}")
            return 0.0

    def step2_green_detection(self, image: np.ndarray) -> Optional[TargetData]:
        """Step 2: Enhanced green target detection with multiple validation methods"""
        logger.info("\n" + "="*60)
        logger.info("STEP 2: ENHANCED GREEN TARGET DETECTION")
        logger.info("="*60)

        if image is None:
            logger.error("No image available for green detection!")
            return None

        try:
            # Convert to HSV for better color detection
            hsv = cv2.cvtColor(image, cv2.COLOR_BGR2HSV)

            # Get color ranges from config
            lower_green = np.array(self.config['green_detection']['lower_hsv'])
            upper_green = np.array(self.config['green_detection']['upper_hsv'])
            min_area = self.config['green_detection']['min_area']

            # Create green mask
            green_mask = cv2.inRange(hsv, lower_green, upper_green)

            # Apply morphological operations to clean up the mask
            green_mask = self._apply_morphological_operations(green_mask)

            # Find contours
            contours, _ = cv2.findContours(green_mask, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)

            if not contours:
                logger.warning("No green contours found!")
                return None

            # Evaluate all contours and find the best candidate
            best_contour = None
            best_score = 0
            best_area = 0

            valid_contours = []

            for contour in contours:
                area = cv2.contourArea(contour)

                # Filter by minimum area
                if area < min_area:
                    continue

                # Calculate confidence score
                confidence = self._calculate_contour_confidence(contour, area)

                # Combined score (area weight + confidence)
                score = area * 0.7 + confidence * area * 0.3

                valid_contours.append({
                    'contour': contour,
                    'area': area,
                    'confidence': confidence,
                    'score': score
                })

                if score > best_score:
                    best_score = score
                    best_contour = contour
                    best_area = area

            if best_contour is None:
                logger.warning(f"No valid green targets found (min area: {min_area})")
                return None

            # Get bounding rectangle
            x, y, w, h = cv2.boundingRect(best_contour)

            # Calculate confidence for the best contour
            confidence = self._calculate_contour_confidence(best_contour, best_area)

            # Create visualization
            result_image = image.copy()

            # Draw all valid contours in light green
            for contour_data in valid_contours:
                cv2.drawContours(result_image, [contour_data['contour']], -1, (0, 200, 0), 1)

            # Draw best contour in bright green
            cv2.drawContours(result_image, [best_contour], -1, (0, 255, 0), 3)
            cv2.rectangle(result_image, (x, y), (x + w, y + h), (0, 255, 0), 2)

            # Add detailed text information
            cv2.putText(result_image, f'Target: {w}x{h} (conf: {confidence:.2f})',
                       (x, y-10), cv2.FONT_HERSHEY_SIMPLEX, 0.8, (0, 255, 0), 2)
            cv2.putText(result_image, f'Area: {best_area:.0f}px',
                       (x, y+h+20), cv2.FONT_HERSHEY_SIMPLEX, 0.6, (0, 255, 0), 2)

            # Save result
            output_dir = self._ensure_output_directory()
            output_path = os.path.join(output_dir, "step2_green_detection.jpg")
            cv2.imwrite(output_path, result_image)
            logger.info(f"Saved: {output_path}")

            # Create target data object
            target_data = TargetData(
                bbox=(x, y, w, h),
                area=best_area,
                center=(x + w//2, y + h//2),
                confidence=confidence
            )

            logger.info(f"Green target detected:")
            logger.info(f"  Position: ({x}, {y})")
            logger.info(f"  Size: {w} x {h} pixels")
            logger.info(f"  Area: {best_area:.0f} pixels")
            logger.info(f"  Confidence: {confidence:.3f}")
            logger.info(f"  Valid contours found: {len(valid_contours)}")

            self.step_images['green_detection'] = result_image
            self.results['green_data'] = target_data
            return target_data

        except Exception as e:
            logger.error(f"Error in step2_green_detection: {str(e)}")
            return None
    
    def step3_crop_green_target(self, image, green_data):
        """Step 3: Crop the green target area"""
        print("\n" + "="*60)
        print("STEP 3: CROP GREEN TARGET")
        print("="*60)
        
        if green_data is None:
            print("No green target data available!")
            return None
        
        x, y, w, h = green_data['bbox']
        
        # Crop the green target area
        cropped_image = image[y:y+h, x:x+w]
        
        print(f"Cropped green target area:")
        print(f"  Original position: ({x}, {y})")
        print(f"  Cropped size: {w} x {h} pixels")
        
        # Save cropped image
        cv2.imwrite("step3_cropped_green.jpg", cropped_image)
        print("  Saved: step3_cropped_green.jpg")
        
        self.step_images['cropped'] = cropped_image
        return cropped_image
    
    def step4_double_size(self, cropped_image):
        """Step 4: Double the size of cropped image"""
        print("\n" + "="*60)
        print("STEP 4: DOUBLE IMAGE SIZE")
        print("="*60)
        
        if cropped_image is None:
            print("No cropped image available!")
            return None
        
        # Get original size
        h, w = cropped_image.shape[:2]
        
        # Double the size
        doubled_image = cv2.resize(cropped_image, (w*2, h*2), interpolation=cv2.INTER_CUBIC)
        
        print(f"Image size doubled:")
        print(f"  Original size: {w} x {h} pixels")
        print(f"  New size: {w*2} x {h*2} pixels")
        
        # Save doubled image
        cv2.imwrite("step4_doubled_size.jpg", doubled_image)
        print("  Saved: step4_doubled_size.jpg")
        
        self.step_images['doubled'] = doubled_image
        return doubled_image
    
    def step5_binary_conversion(self, doubled_image, user_threshold=127):
        """Step 5: Convert to binary image with threshold"""
        print("\n" + "="*60)
        print("STEP 5: BINARY CONVERSION")
        print("="*60)
        
        if doubled_image is None:
            print("No doubled image available!")
            return None
        
        # Convert to grayscale
        gray_image = cv2.cvtColor(doubled_image, cv2.COLOR_BGR2GRAY)
        
        # Apply threshold
        _, binary_image = cv2.threshold(gray_image, user_threshold, 255, cv2.THRESH_BINARY)
        
        # Fill 100 pixels from all sides with black
        binary_image = self.fill_edges_black(binary_image, margin=100)
        
        print(f"Binary conversion with threshold {user_threshold}:")
        print(f"  White pixels: {np.sum(binary_image == 255)}")
        print(f"  Black pixels: {np.sum(binary_image == 0)}")
        
        # Save binary image
        cv2.imwrite("step5_binary.jpg", binary_image)
        print("  Saved: step5_binary.jpg")
        
        # Save grayscale image for reference
        cv2.imwrite("step5_grayscale.jpg", gray_image)
        print("  Saved: step5_grayscale.jpg")
        
        self.step_images['binary'] = binary_image
        self.step_images['grayscale'] = gray_image
        return binary_image, gray_image
    
    def step5_sliding_threshold(self, doubled_image):
        """Step 5b: Create sliding threshold options"""
        print("\n" + "="*60)
        print("STEP 5B: SLIDING THRESHOLD OPTIONS")
        print("="*60)
        
        if doubled_image is None:
            print("No doubled image available!")
            return None
        
        # Convert to grayscale
        gray_image = cv2.cvtColor(doubled_image, cv2.COLOR_BGR2GRAY)
        
        # Test different threshold ranges
        threshold_ranges = [
            (140, 160),
            (130, 170),
            (120, 180),
            (110, 190),
            (100, 200)
        ]
        
        for lower, upper in threshold_ranges:
            print(f"\n" + "="*40)
            print(f"THRESHOLD RANGE: {lower} - {upper}")
            print("="*40)
            
            # Create binary image
            _, binary = cv2.threshold(gray_image, lower, 255, cv2.THRESH_BINARY)
            
            # Fill 100 pixels from all sides with black
            binary = self.fill_edges_black(binary, margin=100)
            
            # Count pixels
            white_pixels = np.sum(binary == 255)
            black_pixels = np.sum(binary == 0)
            total_pixels = white_pixels + black_pixels
            white_percentage = (white_pixels / total_pixels) * 100
            
            print(f"  White pixels: {white_pixels}")
            print(f"  Black pixels: {black_pixels}")
            print(f"  White percentage: {white_percentage:.2f}%")
            
            # Save binary image
            filename = f"step5_sliding_threshold_l{lower}_u{upper}.jpg"
            cv2.imwrite(filename, binary)
            print(f"  Saved: {filename}")
            
            # Also create inverted version
            _, binary_inv = cv2.threshold(gray_image, lower, 255, cv2.THRESH_BINARY_INV)
            binary_inv = self.fill_edges_black(binary_inv, margin=100)
            filename_inv = f"step5_sliding_threshold_inv_l{lower}_u{upper}.jpg"
            cv2.imwrite(filename_inv, binary_inv)
            print(f"  Saved: {filename_inv}")
        
        print("\n" + "="*60)
        print("SLIDING THRESHOLD OPTIONS COMPLETE!")
        print("="*60)
        
        return gray_image
    
    def fill_edges_black(self, image, margin=10):
        """Fill margin pixels from all sides with black"""
        if image is None:
            return None
        
        h, w = image.shape[:2]
        
        # Create a copy of the image
        result = image.copy()
        
        # Fill top margin
        result[:margin, :] = 0
        
        # Fill bottom margin
        result[h-margin:, :] = 0
        
        # Fill left margin
        result[:, :margin] = 0
        
        # Fill right margin
        result[:, w-margin:] = 0
        
        return result
    
    def step6_skip_inversion(self, gray_image):
        """Step 6: Skip inversion - keep original grayscale image"""
        print("\n" + "="*60)
        print("STEP 6: SKIP INVERSION - KEEP ORIGINAL IMAGE")
        print("="*60)
        
        if gray_image is None:
            print("No grayscale image available!")
            return None
        
        print("Inversion step skipped - keeping original grayscale image")
        
        # Save the original grayscale image
        cv2.imwrite("step6_original_grayscale.jpg", gray_image)
        print("  Saved: step6_original_grayscale.jpg")
        
        self.step_images['original_grayscale'] = gray_image
        return gray_image
    
    def _calculate_bullet_confidence(self, stats: np.ndarray, area: float) -> float:
        """Calculate confidence score for bullet hole detection"""
        try:
            min_area = self.config['bullet_detection']['min_area']
            max_area = self.config['bullet_detection']['max_area']

            # Area score (prefer medium-sized holes)
            if area < min_area or area > max_area:
                return 0.0

            # Optimal area range (adjust based on your target type)
            optimal_min, optimal_max = 50, 300
            if optimal_min <= area <= optimal_max:
                area_score = 1.0
            else:
                area_score = max(0.3, 1.0 - abs(area - (optimal_min + optimal_max) / 2) / max_area)

            # Aspect ratio score (prefer circular holes)
            w = stats[cv2.CC_STAT_WIDTH]
            h = stats[cv2.CC_STAT_HEIGHT]
            aspect_ratio = min(w, h) / max(w, h) if max(w, h) > 0 else 0
            aspect_score = aspect_ratio  # Closer to 1 is better (more circular)

            # Size consistency score
            size_score = min(w, h) / max(w, h) if max(w, h) > 0 else 0

            # Combined confidence
            confidence = (area_score * 0.5 + aspect_score * 0.3 + size_score * 0.2)
            return min(confidence, 1.0)

        except Exception as e:
            logger.warning(f"Error calculating bullet confidence: {e}")
            return 0.0

    def _apply_adaptive_threshold(self, image: np.ndarray) -> Tuple[np.ndarray, np.ndarray]:
        """Apply adaptive thresholding for better bullet detection"""
        # Standard binary threshold
        _, binary_standard = cv2.threshold(image, 127, 255, cv2.THRESH_BINARY)

        # Adaptive threshold for varying lighting conditions
        binary_adaptive = cv2.adaptiveThreshold(
            image, 255, cv2.ADAPTIVE_THRESH_GAUSSIAN_C, cv2.THRESH_BINARY, 11, 2
        )

        return binary_standard, binary_adaptive

    def step7_detect_bullets(self, grayscale_image: np.ndarray) -> List[BulletHole]:
        """Step 7: Enhanced bullet detection with confidence scoring and multiple methods"""
        logger.info("\n" + "="*60)
        logger.info("STEP 7: ENHANCED BULLET DETECTION")
        logger.info("="*60)

        if grayscale_image is None:
            logger.error("No image available for bullet detection!")
            return []

        try:
            # Use configuration parameters
            min_area = self.config['bullet_detection']['min_area']
            max_area = self.config['bullet_detection']['max_area']
            confidence_threshold = self.config['bullet_detection']['confidence_threshold']
            edge_margin = self.config['bullet_detection']['edge_margin']

            # Apply edge margin (fill edges with neutral value)
            bullet_detection_image = self.fill_edges_black(grayscale_image, margin=edge_margin)

            logger.info(f"Using image for bullet detection: {bullet_detection_image.shape}")
            logger.info(f"Detection parameters: min_area={min_area}, max_area={max_area}")
            logger.info(f"Confidence threshold: {confidence_threshold}")

            bullets = []

            # Apply adaptive thresholding if enabled
            if self.config['bullet_detection']['adaptive_threshold']:
                binary_standard, binary_adaptive = self._apply_adaptive_threshold(bullet_detection_image)
            else:
                _, binary_standard = cv2.threshold(bullet_detection_image, 127, 255, cv2.THRESH_BINARY)
                binary_adaptive = binary_standard

            # 1. Detect WHITE HOLES (white spots on dark background)
            logger.info("\n--- DETECTING WHITE HOLES ---")

            # Find connected components (white spots)
            num_labels_white, _, stats_white, centroids_white = cv2.connectedComponentsWithStats(
                binary_standard, connectivity=8
            )

            logger.info(f"Found {num_labels_white-1} white connected components")

            white_holes = []
            for i in range(1, num_labels_white):  # Skip background (label 0)
                area = stats_white[i, cv2.CC_STAT_AREA]
                x = stats_white[i, cv2.CC_STAT_LEFT]
                y = stats_white[i, cv2.CC_STAT_TOP]
                w = stats_white[i, cv2.CC_STAT_WIDTH]
                h = stats_white[i, cv2.CC_STAT_HEIGHT]

                # Calculate confidence score
                confidence = self._calculate_bullet_confidence(stats_white[i], area)

                logger.debug(f"  White Component {i}: Area={area}, Pos=({x},{y}), Size={w}x{h}, Conf={confidence:.3f}")

                # Filter by area and confidence
                if min_area <= area <= max_area and confidence >= confidence_threshold:
                    white_hole = BulletHole(
                        id=f"W{i}",
                        area=float(area),
                        position=(int(x), int(y)),
                        size=(int(w), int(h)),
                        center=(int(centroids_white[i][0]), int(centroids_white[i][1])),
                        type='white_hole',
                        confidence=confidence
                    )
                    white_holes.append(white_hole)
                    bullets.append(white_hole)
                else:
                    logger.debug(f"    Rejected: area={area} (range: {min_area}-{max_area}), conf={confidence:.3f}")

            logger.info(f"Valid white holes: {len(white_holes)}")
        
            # 2. Detect BLACK HOLES (black spots on light background)
            logger.info("\n--- DETECTING BLACK HOLES ---")
            _, binary_black = cv2.threshold(bullet_detection_image, 127, 255, cv2.THRESH_BINARY_INV)

            # Find connected components (black spots)
            num_labels_black, _, stats_black, centroids_black = cv2.connectedComponentsWithStats(
                binary_black, connectivity=8
            )

            logger.info(f"Found {num_labels_black-1} black connected components")

            black_holes = []
            for i in range(1, num_labels_black):  # Skip background (label 0)
                area = stats_black[i, cv2.CC_STAT_AREA]
                x = stats_black[i, cv2.CC_STAT_LEFT]
                y = stats_black[i, cv2.CC_STAT_TOP]
                w = stats_black[i, cv2.CC_STAT_WIDTH]
                h = stats_black[i, cv2.CC_STAT_HEIGHT]

                # Calculate confidence score
                confidence = self._calculate_bullet_confidence(stats_black[i], area)

                logger.debug(f"  Black Component {i}: Area={area}, Pos=({x},{y}), Size={w}x{h}, Conf={confidence:.3f}")

                # Filter by area and confidence
                if min_area <= area <= max_area and confidence >= confidence_threshold:
                    black_hole = BulletHole(
                        id=f"B{i}",
                        area=float(area),
                        position=(int(x), int(y)),
                        size=(int(w), int(h)),
                        center=(int(centroids_black[i][0]), int(centroids_black[i][1])),
                        type='black_hole',
                        confidence=confidence
                    )
                    black_holes.append(black_hole)
                    bullets.append(black_hole)
                else:
                    logger.debug(f"    Rejected: area={area} (range: {min_area}-{max_area}), conf={confidence:.3f}")

            logger.info(f"Valid black holes: {len(black_holes)}")
        
            # Create enhanced visualization
            vis_image = cv2.cvtColor(bullet_detection_image, cv2.COLOR_GRAY2BGR)

            # Draw white holes in green with confidence-based styling
            for bullet in white_holes:
                x, y = bullet.position
                w, h = bullet.size
                confidence = bullet.confidence

                # Color intensity based on confidence
                color_intensity = int(255 * confidence)
                color = (0, color_intensity, 0)

                # Draw filled rectangle with transparency
                overlay = vis_image.copy()
                cv2.rectangle(overlay, (x, y), (x + w, y + h), color, -1)
                cv2.addWeighted(overlay, 0.3, vis_image, 0.7, 0, vis_image)

                # Draw border (thicker for higher confidence)
                border_thickness = max(1, int(3 * confidence))
                cv2.rectangle(vis_image, (x, y), (x + w, y + h), color, border_thickness)

                # Add text with confidence
                text = f"{bullet.id} ({w}x{h}) {confidence:.2f}"
                cv2.putText(vis_image, text, (x, y-10), cv2.FONT_HERSHEY_SIMPLEX, 0.6, color, 2)

            # Draw black holes in red with confidence-based styling
            for bullet in black_holes:
                x, y = bullet.position
                w, h = bullet.size
                confidence = bullet.confidence

                # Color intensity based on confidence
                color_intensity = int(255 * confidence)
                color = (0, 0, color_intensity)

                # Draw filled rectangle with transparency
                overlay = vis_image.copy()
                cv2.rectangle(overlay, (x, y), (x + w, y + h), color, -1)
                cv2.addWeighted(overlay, 0.3, vis_image, 0.7, 0, vis_image)

                # Draw border (thicker for higher confidence)
                border_thickness = max(1, int(3 * confidence))
                cv2.rectangle(vis_image, (x, y), (x + w, y + h), color, border_thickness)

                # Add text with confidence
                text = f"{bullet.id} ({w}x{h}) {confidence:.2f}"
                cv2.putText(vis_image, text, (x, y-10), cv2.FONT_HERSHEY_SIMPLEX, 0.6, color, 2)

            # Add comprehensive summary
            total_bullets = len(bullets)
            avg_confidence = np.mean([b.confidence for b in bullets]) if bullets else 0

            summary_lines = [
                f"Total Bullets: {total_bullets} (W:{len(white_holes)}, B:{len(black_holes)})",
                f"Avg Confidence: {avg_confidence:.3f}",
                f"Threshold: {confidence_threshold}"
            ]

            for i, line in enumerate(summary_lines):
                y_pos = 30 + i * 25
                cv2.putText(vis_image, line, (10, y_pos), cv2.FONT_HERSHEY_SIMPLEX, 0.7, (255, 255, 255), 2)

            # Save visualization
            output_dir = self._ensure_output_directory()
            vis_path = os.path.join(output_dir, "step7_bullet_detection.jpg")
            cv2.imwrite(vis_path, vis_image)
        
            # Create detailed analysis visualization
            detailed_vis = cv2.cvtColor(bullet_detection_image, cv2.COLOR_GRAY2BGR)

            # Draw detection areas with detailed information
            for bullet in white_holes:
                x, y = bullet.position
                w, h = bullet.size
                confidence = bullet.confidence

                cv2.rectangle(detailed_vis, (x, y), (x + w, y + h), (0, 255, 0), 2)
                cv2.putText(detailed_vis, f"WHITE {bullet.id}", (x, y-25),
                           cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 255, 0), 1)
                cv2.putText(detailed_vis, f"Area: {bullet.area:.0f}px", (x, y-10),
                           cv2.FONT_HERSHEY_SIMPLEX, 0.4, (0, 255, 0), 1)
                cv2.putText(detailed_vis, f"Conf: {confidence:.3f}", (x, y+h+15),
                           cv2.FONT_HERSHEY_SIMPLEX, 0.4, (0, 255, 0), 1)

            for bullet in black_holes:
                x, y = bullet.position
                w, h = bullet.size
                confidence = bullet.confidence

                cv2.rectangle(detailed_vis, (x, y), (x + w, y + h), (0, 0, 255), 2)
                cv2.putText(detailed_vis, f"BLACK {bullet.id}", (x, y-25),
                           cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 0, 255), 1)
                cv2.putText(detailed_vis, f"Area: {bullet.area:.0f}px", (x, y-10),
                           cv2.FONT_HERSHEY_SIMPLEX, 0.4, (0, 0, 255), 1)
                cv2.putText(detailed_vis, f"Conf: {confidence:.3f}", (x, y+h+15),
                           cv2.FONT_HERSHEY_SIMPLEX, 0.4, (0, 0, 255), 1)

            detailed_path = os.path.join(output_dir, "step7_detailed_detection.jpg")
            cv2.imwrite(detailed_path, detailed_vis)

            # Log comprehensive results
            logger.info(f"\nBullet detection results:")
            logger.info(f"  White holes detected: {len(white_holes)} (confidence filtered)")
            logger.info(f"  Black holes detected: {len(black_holes)} (confidence filtered)")
            logger.info(f"  Total valid bullets: {len(bullets)}")
            logger.info(f"  Average confidence: {avg_confidence:.3f}")
            logger.info(f"  Detection parameters used:")
            logger.info(f"    - Min area: {min_area}, Max area: {max_area}")
            logger.info(f"    - Confidence threshold: {confidence_threshold}")
            logger.info(f"    - Edge margin: {edge_margin}")

            # Store results
            self.results['bullets'] = bullets
            self.results['white_holes'] = white_holes
            self.results['black_holes'] = black_holes
            self.results['detection_stats'] = {
                'total_bullets': len(bullets),
                'white_holes': len(white_holes),
                'black_holes': len(black_holes),
                'average_confidence': avg_confidence,
                'parameters': {
                    'min_area': min_area,
                    'max_area': max_area,
                    'confidence_threshold': confidence_threshold,
                    'edge_margin': edge_margin
                }
            }

            return bullets

        except Exception as e:
            logger.error(f"Error in step7_detect_bullets: {str(e)}")
            return []
    
    def _calculate_shooting_accuracy(self, bullets: List[BulletHole], target_center: Tuple[int, int]) -> Dict[str, Any]:
        """Calculate shooting accuracy metrics"""
        if not bullets:
            return {'accuracy_score': 0.0, 'average_distance': 0.0, 'grouping_size': 0.0}

        # Calculate distances from target center
        distances = []
        for bullet in bullets:
            dx = bullet.center[0] - target_center[0]
            dy = bullet.center[1] - target_center[1]
            distance = np.sqrt(dx*dx + dy*dy)
            distances.append(distance)

        avg_distance = np.mean(distances)
        max_distance = np.max(distances)
        min_distance = np.min(distances)

        # Calculate grouping size (spread of shots)
        if len(bullets) > 1:
            bullet_centers = np.array([bullet.center for bullet in bullets])
            center_of_group = np.mean(bullet_centers, axis=0)
            group_distances = [np.sqrt((center[0] - center_of_group[0])**2 +
                                     (center[1] - center_of_group[1])**2)
                             for center in bullet_centers]
            grouping_size = np.max(group_distances) * 2  # Diameter of group
        else:
            grouping_size = 0.0

        # Calculate accuracy score (inverse of average distance, normalized)
        accuracy_score = max(0.0, 1.0 - (avg_distance / 200.0))  # Adjust 200 based on target size

        return {
            'accuracy_score': accuracy_score,
            'average_distance': avg_distance,
            'max_distance': max_distance,
            'min_distance': min_distance,
            'grouping_size': grouping_size,
            'shot_count': len(bullets)
        }

    def save_results(self):
        """Save comprehensive results to JSON file with enhanced metadata"""
        logger.info("\n" + "="*60)
        logger.info("SAVING COMPREHENSIVE RESULTS")
        logger.info("="*60)

        try:
            # Convert results to JSON-serializable format
            results_for_json = {
                'analysis_metadata': {
                    'timestamp': str(np.datetime64('now')),
                    'version': '2.0',
                    'configuration': self.config
                }
            }

            # Image information
            if 'image_info' in self.results:
                results_for_json['image_info'] = self.results['image_info']

            # Green target data
            if 'green_data' in self.results:
                green_data = self.results['green_data']
                results_for_json['green_data'] = {
                    'bbox': green_data.bbox,
                    'area': green_data.area,
                    'center': green_data.center,
                    'confidence': green_data.confidence
                }

            # Bullet detection results
            if 'bullets' in self.results:
                bullets_data = []
                for bullet in self.results['bullets']:
                    bullets_data.append({
                        'id': bullet.id,
                        'center': bullet.center,
                        'position': bullet.position,
                        'size': bullet.size,
                        'area': bullet.area,
                        'type': bullet.type,
                        'confidence': bullet.confidence
                    })
                results_for_json['bullets'] = bullets_data

                # Calculate accuracy metrics if we have target center
                if 'green_data' in self.results:
                    target_center = self.results['green_data'].center
                    accuracy_metrics = self._calculate_shooting_accuracy(self.results['bullets'], target_center)
                    results_for_json['accuracy_metrics'] = accuracy_metrics

            # Detection statistics
            if 'detection_stats' in self.results:
                results_for_json['detection_stats'] = self.results['detection_stats']

            # Save to JSON with timestamp
            output_dir = self._ensure_output_directory()
            output_path = os.path.join(output_dir, "complete_analysis_results.json")

            with open(output_path, "w") as f:
                json.dump(results_for_json, f, indent=2, default=str)

            logger.info(f"Saved comprehensive results: {output_path}")

            # Also save a summary report
            self._save_summary_report(results_for_json, output_dir)

        except Exception as e:
            logger.error(f"Error saving results: {str(e)}")

    def _save_summary_report(self, results: Dict[str, Any], output_dir: str):
        """Save a human-readable summary report"""
        try:
            report_path = os.path.join(output_dir, "analysis_summary.txt")

            with open(report_path, "w") as f:
                f.write("SHOOTING ANALYSIS SUMMARY REPORT\n")
                f.write("=" * 50 + "\n\n")

                # Image info
                if 'image_info' in results:
                    img_info = results['image_info']
                    f.write(f"Image: {img_info['path']}\n")
                    f.write(f"Dimensions: {img_info['width']} x {img_info['height']}\n")
                    f.write(f"File size: {img_info['file_size'] / 1024:.1f} KB\n\n")

                # Target detection
                if 'green_data' in results:
                    green = results['green_data']
                    f.write(f"Target Detection:\n")
                    f.write(f"  Position: {green['bbox'][:2]}\n")
                    f.write(f"  Size: {green['bbox'][2:]} pixels\n")
                    f.write(f"  Confidence: {green['confidence']:.3f}\n\n")

                # Bullet detection
                if 'bullets' in results:
                    bullets = results['bullets']
                    f.write(f"Bullet Detection:\n")
                    f.write(f"  Total bullets found: {len(bullets)}\n")

                    white_bullets = [b for b in bullets if b['type'] == 'white_hole']
                    black_bullets = [b for b in bullets if b['type'] == 'black_hole']

                    f.write(f"  White holes: {len(white_bullets)}\n")
                    f.write(f"  Black holes: {len(black_bullets)}\n")

                    if bullets:
                        avg_conf = np.mean([b['confidence'] for b in bullets])
                        f.write(f"  Average confidence: {avg_conf:.3f}\n")

                # Accuracy metrics
                if 'accuracy_metrics' in results:
                    acc = results['accuracy_metrics']
                    f.write(f"\nAccuracy Analysis:\n")
                    f.write(f"  Accuracy score: {acc['accuracy_score']:.3f}\n")
                    f.write(f"  Average distance from center: {acc['average_distance']:.1f} pixels\n")
                    f.write(f"  Shot grouping size: {acc['grouping_size']:.1f} pixels\n")
                    f.write(f"  Best shot distance: {acc['min_distance']:.1f} pixels\n")
                    f.write(f"  Worst shot distance: {acc['max_distance']:.1f} pixels\n")

            logger.info(f"Saved summary report: {report_path}")

        except Exception as e:
            logger.error(f"Error saving summary report: {str(e)}")
    
    def run_complete_analysis(self, image_path: str, custom_config: Optional[Dict[str, Any]] = None) -> bool:
        """Run complete step-by-step analysis with enhanced error handling and configuration"""
        logger.info("COMPLETE SHOOTING ANALYSIS v2.0")
        logger.info("="*60)
        logger.info(f"Image: {image_path}")

        # Update configuration if provided
        if custom_config:
            self.config.update(custom_config)
            logger.info("Custom configuration applied")

        logger.info(f"Configuration:")
        logger.info(f"  Green detection: {self.config['green_detection']}")
        logger.info(f"  Bullet detection: {self.config['bullet_detection']}")
        logger.info(f"  Image processing: {self.config['image_processing']}")
        logger.info("="*60)

        try:
            # Step 1: Load and validate original image
            logger.info("\n🔍 Starting Step 1: Image Loading")
            image = self.step1_original_image(image_path)
            if image is None:
                logger.error("Failed to load image. Analysis terminated.")
                return False

            # Step 2: Enhanced green target detection
            logger.info("\n🎯 Starting Step 2: Target Detection")
            green_data = self.step2_green_detection(image)
            if green_data is None:
                logger.error("Failed to detect green target. Analysis terminated.")
                return False

            # Step 3: Crop green target area
            logger.info("\n✂️ Starting Step 3: Target Cropping")
            cropped_image = self.step3_crop_green_target(image, green_data)
            if cropped_image is None:
                logger.error("Failed to crop target area. Analysis terminated.")
                return False

            # Step 4: Enhance image resolution
            logger.info("\n🔍 Starting Step 4: Image Enhancement")
            doubled_image = self.step4_double_size(cropped_image)
            if doubled_image is None:
                logger.error("Failed to enhance image. Analysis terminated.")
                return False

            # Step 5: Image preprocessing
            logger.info("\n⚙️ Starting Step 5: Image Preprocessing")
            threshold_value = self.config['image_processing']['threshold_value']
            binary_image, gray_image = self.step5_binary_conversion(doubled_image, threshold_value)
            if binary_image is None:
                logger.error("Failed to preprocess image. Analysis terminated.")
                return False

            # Step 5b: Optional sliding threshold analysis
            if self.config['output']['save_intermediate_steps']:
                logger.info("\n📊 Starting Step 5b: Threshold Analysis")
                self.step5_sliding_threshold(doubled_image)

            # Step 6: Prepare for bullet detection
            logger.info("\n🔧 Starting Step 6: Detection Preparation")
            original_grayscale = self.step6_skip_inversion(gray_image)
            if original_grayscale is None:
                logger.error("Failed to prepare for detection. Analysis terminated.")
                return False

            # Step 7: Enhanced bullet detection
            logger.info("\n🎯 Starting Step 7: Bullet Detection")
            bullets = self.step7_detect_bullets(original_grayscale)

            # Save comprehensive results
            logger.info("\n💾 Saving Results")
            self.save_results()

            # Final summary
            logger.info("\n" + "="*60)
            logger.info("✅ ANALYSIS COMPLETE!")
            logger.info("="*60)
            logger.info(f"📊 Results Summary:")
            logger.info(f"  • Target detected: ✅ (confidence: {green_data.confidence:.3f})")
            logger.info(f"  • Bullets detected: {len(bullets)}")

            if bullets:
                avg_confidence = np.mean([b.confidence for b in bullets])
                logger.info(f"  • Average bullet confidence: {avg_confidence:.3f}")

                # Calculate and display accuracy if possible
                if 'accuracy_metrics' in self.results:
                    acc = self.results['accuracy_metrics']
                    logger.info(f"  • Accuracy score: {acc['accuracy_score']:.3f}")
                    logger.info(f"  • Shot grouping: {acc['grouping_size']:.1f} pixels")

            output_dir = self.config['output']['output_directory']
            logger.info(f"\n📁 Output files saved to: {output_dir}/")
            logger.info(f"  • Analysis results: complete_analysis_results.json")
            logger.info(f"  • Summary report: analysis_summary.txt")
            logger.info(f"  • Visualization images: step*.jpg")

            return True

        except Exception as e:
            logger.error(f"Critical error during analysis: {str(e)}")
            logger.error("Analysis terminated due to unexpected error.")
            return False

    def get_analysis_summary(self) -> Dict[str, Any]:
        """Get a summary of the current analysis results"""
        summary = {
            'has_results': bool(self.results),
            'target_detected': 'green_data' in self.results,
            'bullets_detected': 'bullets' in self.results,
            'bullet_count': len(self.results.get('bullets', [])),
            'configuration': self.config
        }

        if 'green_data' in self.results:
            summary['target_confidence'] = self.results['green_data'].confidence

        if 'bullets' in self.results:
            bullets = self.results['bullets']
            if bullets:
                summary['average_bullet_confidence'] = np.mean([b.confidence for b in bullets])
                summary['bullet_types'] = {
                    'white_holes': len([b for b in bullets if b.type == 'white_hole']),
                    'black_holes': len([b for b in bullets if b.type == 'black_hole'])
                }

        return summary

    def export_results_csv(self, filename: Optional[str] = None) -> str:
        """Export bullet detection results to CSV format"""
        if 'bullets' not in self.results:
            logger.warning("No bullet detection results to export")
            return ""

        if filename is None:
            output_dir = self._ensure_output_directory()
            filename = os.path.join(output_dir, "bullet_detection_results.csv")

        try:
            import csv

            with open(filename, 'w', newline='') as csvfile:
                fieldnames = ['id', 'type', 'center_x', 'center_y', 'position_x', 'position_y',
                             'width', 'height', 'area', 'confidence']
                writer = csv.DictWriter(csvfile, fieldnames=fieldnames)

                writer.writeheader()
                for bullet in self.results['bullets']:
                    writer.writerow({
                        'id': bullet.id,
                        'type': bullet.type,
                        'center_x': bullet.center[0],
                        'center_y': bullet.center[1],
                        'position_x': bullet.position[0],
                        'position_y': bullet.position[1],
                        'width': bullet.size[0],
                        'height': bullet.size[1],
                        'area': bullet.area,
                        'confidence': bullet.confidence
                    })

            logger.info(f"Exported bullet results to CSV: {filename}")
            return filename

        except Exception as e:
            logger.error(f"Error exporting to CSV: {str(e)}")
            return ""

def create_analyzer_with_config(config_file: Optional[str] = None) -> ShootingAnalyzer:
    """Factory function to create analyzer with optional config file"""
    config = None

    if config_file and os.path.exists(config_file):
        try:
            with open(config_file, 'r') as f:
                config = json.load(f)
            logger.info(f"Loaded configuration from: {config_file}")
        except Exception as e:
            logger.warning(f"Failed to load config file {config_file}: {e}")

    return ShootingAnalyzer(config)

def main():
    """Enhanced main function with better configuration and error handling"""
    # Set up logging level based on environment
    import sys
    if '--debug' in sys.argv:
        logging.getLogger().setLevel(logging.DEBUG)
        logger.info("Debug logging enabled")

    # Create analyzer with optional config
    config_file = "analyzer_config.json" if os.path.exists("analyzer_config.json") else None
    analyzer = create_analyzer_with_config(config_file)

    # Default image path
    image_path = "Shoot-image4.jpg"

    # Check for command line arguments
    if len(sys.argv) > 1 and not sys.argv[1].startswith('--'):
        image_path = sys.argv[1]

    if not os.path.exists(image_path):
        logger.error(f"Image file not found: {image_path}")
        logger.info("Available images in current directory:")
        for file in os.listdir('.'):
            if file.lower().endswith(('.jpg', '.jpeg', '.png', '.bmp')):
                logger.info(f"  - {file}")
        return

    # Optional custom configuration for this run
    custom_config = None
    if '--high-precision' in sys.argv:
        custom_config = {
            'bullet_detection': {
                'confidence_threshold': 0.7,
                'min_area': 20,
                'max_area': 1000
            }
        }
        logger.info("High precision mode enabled")

    # Run analysis
    logger.info(f"Starting analysis of: {image_path}")
    success = analyzer.run_complete_analysis(image_path, custom_config)

    if success:
        # Print summary
        summary = analyzer.get_analysis_summary()
        logger.info("\n📋 Analysis Summary:")
        logger.info(f"  Target detected: {'✅' if summary['target_detected'] else '❌'}")
        logger.info(f"  Bullets found: {summary['bullet_count']}")

        if summary['bullet_count'] > 0:
            # Export to CSV
            csv_file = analyzer.export_results_csv()
            if csv_file:
                logger.info(f"  CSV export: {csv_file}")

        logger.info("\n🎯 Analysis completed successfully!")
    else:
        logger.error("❌ Analysis failed!")
        sys.exit(1)

if __name__ == "__main__":
    main()