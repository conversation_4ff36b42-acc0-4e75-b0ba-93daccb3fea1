import cv2
import numpy as np
import matplotlib.pyplot as plt

def detect_green_targets_step2(image_path):
    """Step 2: Green target detection with multiple approaches"""
    print("="*60)
    print("STEP 2: GREEN TARGET DETECTION")
    print("="*60)
    
    # Read the image
    image = cv2.imread(image_path)
    if image is None:
        print(f"Error: Could not read image {image_path}")
        return None
    
    # Convert to HSV
    hsv = cv2.cvtColor(image, cv2.COLOR_BGR2HSV)
    
    # Define different green ranges to test
    green_ranges = {
        'Narrow Green': {
            'lower': np.array([35, 50, 50]),
            'upper': np.array([85, 255, 255])
        },
        'Wide Green': {
            'lower': np.array([30, 40, 40]),
            'upper': np.array([90, 255, 255])
        },
        'Bright Green': {
            'lower': np.array([40, 100, 100]),
            'upper': np.array([80, 255, 255])
        },
        'Dark Green': {
            'lower': np.array([35, 50, 30]),
            'upper': np.array([85, 255, 200])
        }
    }
    
    masks = {}
    contours_data = {}
    
    print(f"Testing {len(green_ranges)} different green detection ranges:")
    
    for name, ranges in green_ranges.items():
        print(f"\nTesting: {name}")
        print(f"  HSV Range: {ranges['lower']} to {ranges['upper']}")
        
        # Create mask
        mask = cv2.inRange(hsv, ranges['lower'], ranges['upper'])
        
        # Clean up mask
        kernel = np.ones((5, 5), np.uint8)
        mask = cv2.morphologyEx(mask, cv2.MORPH_CLOSE, kernel)
        mask = cv2.morphologyEx(mask, cv2.MORPH_OPEN, kernel)
        
        # Find contours
        contours, _ = cv2.findContours(mask, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
        
        # Filter contours by area
        large_contours = []
        for contour in contours:
            area = cv2.contourArea(contour)
            if area > 1000:  # Minimum area threshold
                large_contours.append(contour)
        
        masks[name] = mask
        contours_data[name] = large_contours
        
        print(f"  Total contours: {len(contours)}")
        print(f"  Large contours (>1000px): {len(large_contours)}")
        
        if large_contours:
            areas = [cv2.contourArea(c) for c in large_contours]
            print(f"  Largest area: {max(areas):.0f} pixels")
            print(f"  Average area: {sum(areas)/len(areas):.0f} pixels")
    
    # Create visualization
    fig, axes = plt.subplots(2, 3, figsize=(18, 12))
    
    # Original image
    axes[0,0].imshow(cv2.cvtColor(image, cv2.COLOR_BGR2RGB))
    axes[0,0].set_title('Original Image')
    axes[0,0].axis('off')
    
    # Show masks
    row = 0
    col = 1
    for name, mask in masks.items():
        if col >= 3:
            col = 0
            row += 1
        
        axes[row, col].imshow(mask, cmap='gray')
        axes[row, col].set_title(f'{name}\nMask')
        axes[row, col].axis('off')
        
        # Draw contours on original image
        contour_img = image.copy()
        cv2.drawContours(contour_img, contours_data[name], -1, (0, 255, 0), 3)
        axes[row, col].imshow(cv2.cvtColor(contour_img, cv2.COLOR_BGR2RGB))
        axes[row, col].set_title(f'{name}\nContours')
        axes[row, col].axis('off')
        
        col += 1
    
    plt.tight_layout()
    plt.savefig('step2_green_detection.jpg', dpi=300, bbox_inches='tight')
    print(f"\nGreen detection analysis saved as: step2_green_detection.jpg")
    
    return {
        'masks': masks,
        'contours_data': contours_data,
        'image': image,
        'hsv': hsv
    }

def main():
    """Main function for Step 2"""
    image_path = "Shoot-image4.jpg"
    
    # Check if image exists
    import os
    if not os.path.exists(image_path):
        print(f"Error: {image_path} not found!")
        return
    
    # Run green detection
    results = detect_green_targets_step2(image_path)
    
    if results:
        print(f"\nStep 2 Complete!")
        print(f"Green detection analysis completed")
        print(f"Generated masks and contour data for further analysis")

if __name__ == "__main__":
    main() 